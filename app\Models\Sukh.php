<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;

/**
 * @mixin IdeHelperSukh
 */
class Sukh extends Model
{
    use HasFactory;

    const ID                          = 'id';
    const NAME                        = 'name';
    const REGISTRATION_NUMBER         = 'registration_number';
    const PHONE                       = 'phone';
    const EMAIL                       = 'email';
    const AIMAG_ID                    = 'aimag_id';
    const SOUM_ID                     = 'soum_id';
    const BAG_ID                      = 'bag_id';
    const CODE                        = 'code';

    const RELATION_BAIRS              = 'bairs';
    const RELATION_ORSHIN_SUUGCHES    = 'orshin_suugches';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
        self::REGISTRATION_NUMBER,
        self::PHONE,
        self::EMAIL,
        self::AIMAG_ID,
        self::SOUM_ID,
        self::BAG_ID,
        self::CODE,
    ];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_sukh');
    }

    public function bairs(): HasMany
    {
        return $this->hasMany(Bair::class);
    }

    public function orshin_suugches(): BelongsToMany
    {
        return $this->belongsToMany(OrshinSuugch::class);
    }

    public function aimag(): BelongsTo
    {
        return $this->belongsTo(Aimag::class);
    }

    public function soum(): BelongsTo
    {
        return $this->belongsTo(Soum::class);
    }

    public function bag(): BelongsTo
    {
        return $this->belongsTo(Bag::class);
    }
}
