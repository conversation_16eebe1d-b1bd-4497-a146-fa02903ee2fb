<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Toot;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;
use Illuminate\Database\Eloquent\Model;

class KorpusesRelationManager extends RelationManager
{
    protected static string $relationship = 'korpuses';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Grid::make(2)
                    ->schema([
                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $bairId = $this->getOwnerRecord()->id;
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->required(),
                        Forms\Components\TextInput::make(Korpus::ORDER)
                            ->required()
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule) {
                                    $bairId = $this->getOwnerRecord()->id;
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->label('Дараалал')
                            ->minValue(1)
                            ->numeric()
                            ->maxValue(9999),
                        Forms\Components\TextInput::make(Korpus::BEGIN_TOOT_NUMBER)
                            ->required()
                            ->label('Тоотийн эхлэх дугаар')
                            ->minValue(1)
                            ->lt(Korpus::END_TOOT_NUMBER)
                            ->numeric()
                            ->maxValue(9999)
                            ->live(),
                        Forms\Components\TextInput::make(Korpus::END_TOOT_NUMBER)
                            ->required()
                            ->label('Тоотийн дуусах дугаар')
                            ->minValue(fn ($get) => (int) $get(Korpus::BEGIN_TOOT_NUMBER) + 1)
                            ->numeric()
                            ->maxValue(10000)
                            ->live(),
                    ]),
                    Forms\Components\Repeater::make(Korpus::RELATION_ORCS)
                        ->relationship()
                        ->schema([
                            Forms\Components\TextInput::make(ORC::NUMBER)
                                ->label('Орцны дугаар')
                                ->unique(
                                    ignoreRecord: true,
                                    modifyRuleUsing: function (Unique $rule, callable $get) {
                                        $korpusId = $get('../../id');
                                        return $rule->where('korpus_id', $korpusId);
                                    }
                                )
                                ->numeric()
                                ->required(),
                            Forms\Components\TextInput::make(ORC::BEGIN_TOOT_NUMBER)
                                ->required()
                                ->label('Тоот эхлэх дугаар')
                                ->minValue(fn ($get) => (int) $get('../../'.Korpus::BEGIN_TOOT_NUMBER))
                                ->lt(ORC::END_TOOT_NUMBER)
                                ->numeric()
                                ->maxValue(fn ($get) => (int) $get('../../'.Korpus::END_TOOT_NUMBER) - 1)
                                ->live(),
                            Forms\Components\TextInput::make(ORC::END_TOOT_NUMBER)
                                ->required()
                                ->label('Тоот дуусах дугаар')
                                ->minValue(fn ($get) => (int) $get(ORC::BEGIN_TOOT_NUMBER) + 1)
                                ->numeric()
                                ->maxValue(fn ($get) => (int) $get('../../'.Korpus::END_TOOT_NUMBER))
                                ->live(),
                        ])
                        ->reorderable(false)
                        ->columns(3),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Korpus::ORDER)->label('Дараалал')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus')
                ->after(function (Model $record) {
                    $beginTootNumber = $record->begin_toot_number;
                    $endTootNumber   = $record->end_toot_number;
                    while ($beginTootNumber <= $endTootNumber) {
                        Toot::create([
                            Toot::KORPUS_ID => $record->id,
                            Toot::NUMBER    => $beginTootNumber
                        ]);
                        $beginTootNumber++;
                    }
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->after(function (Model $record) {
                        Toot::where(Toot::KORPUS_ID, $record->id)->delete();
                        $beginTootNumber = $record->begin_toot_number;
                        $endTootNumber   = $record->end_toot_number;
                        while ($beginTootNumber <= $endTootNumber) {
                            Toot::create([
                                Toot::KORPUS_ID => $record->id,
                                Toot::NUMBER    => $beginTootNumber
                            ]);
                            $beginTootNumber++;
                        }
                    }),
                Tables\Actions\DeleteAction::make()
                    ->after(function (Model $record) {
                        Toot::where(Toot::KORPUS_ID, $record->id)->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
