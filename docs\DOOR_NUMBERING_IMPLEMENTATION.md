# Door Numbering System Implementation

## Overview

This document describes the implementation of the comprehensive door numbering system for the Smart Door IoT application. The system supports both automatic and manual door numbering modes with three different numbering types.

## Features

- ✅ **Automatic Door Numbering**: Supports Type 1, Type 2, and Type 3 numbering schemes
- ✅ **Manual Override**: Ability to manually set door numbers when needed
- ✅ **CVSecurity Integration**: Follows existing patterns for CRUD synchronization
- ✅ **Observer-based**: Automatic recalculation when hierarchical models change
- ✅ **Validation**: Comprehensive validation for each numbering type
- ✅ **API Endpoints**: RESTful API for door numbering operations
- ✅ **Error Handling**: Graceful handling of edge cases and failures

## Architecture

### Core Components

1. **DoorNumberingService** (`app/Services/DoorNumbering/DoorNumberingService.php`)
   - Main service for automatic door number calculation
   - Coordinates with calculator implementations
   - Handles database transactions and error recovery

2. **Calculator Implementations**
   - `Type1Calculator`: Korpus-wise consecutive numbering
   - `Type2Calculator`: Orc-wise consecutive numbering  
   - `Type3Calculator`: Floor-wise consecutive numbering

3. **ManualDoorNumberingService** (`app/Services/DoorNumbering/ManualDoorNumberingService.php`)
   - Handles manual door number assignment
   - Validates manual numbers against numbering type constraints
   - Provides single door and bulk door number updates

4. **Observer Integration**
   - `KorpusObserver`: Triggers recalculation on Korpus changes
   - `DavharObserver`: Triggers recalculation on Davhar changes

5. **API Controller** (`app/Http/Controllers/DoorNumberingController.php`)
   - RESTful endpoints for door numbering operations
   - Request validation and error handling

## Database Schema

### New Fields Added

#### Korpus Table
```sql
ALTER TABLE korpuses ADD COLUMN numbering_type INT DEFAULT 1 COMMENT '1=Korpus-wise, 2=Orc-wise, 3=Floor-wise';
ALTER TABLE korpuses ADD COLUMN digit_multiplier INT DEFAULT 100 COMMENT 'Type 3: 10 (2-digit), 100 (3-digit), 1000 (4-digit)';
```

#### Davhar Table
```sql
ALTER TABLE davhars ADD COLUMN floor_number INT NULL COMMENT 'Floor identifier for Type 3 numbering (1, 2, 3, ...)';
```

## API Endpoints

### Automatic Door Numbering

#### Generate Door Numbers
```http
POST /api/door-numbering/generate
Content-Type: application/json

{
    "korpus_id": 1,
    "regenerate_existing": false
}
```

#### Validate Configuration
```http
GET /api/door-numbering/validate/{korpusId}
```

#### Get Statistics
```http
GET /api/door-numbering/statistics/{korpusId}
```

### Manual Door Numbering

#### Set Davhar Door Numbers
```http
POST /api/door-numbering/set-davhar-doors
Content-Type: application/json

{
    "davhar_id": 1,
    "door_numbers": [101, 102, 103, 104, 105, 106],
    "validate_only": false
}
```

#### Set Single Door Number
```http
POST /api/door-numbering/set-single-door
Content-Type: application/json

{
    "korpus_id": 1,
    "davhar_id": 1,
    "old_number": 101,
    "new_number": 111
}
```

## Usage Examples

### Type 1: Korpus-wise Consecutive Numbering

```php
// Create Korpus with Type 1 numbering
$korpus = Korpus::create([
    'bair_id' => $bair->id,
    'name' => 'Block A',
    'numbering_type' => 1,
    'begin_toot_number' => 1,
    'end_toot_number' => 192
]);

// Create Orcs and Davhars
$orc1 = Orc::create(['korpus_id' => $korpus->id, 'number' => '1']);
$orc2 = Orc::create(['korpus_id' => $korpus->id, 'number' => '2']);

Davhar::create(['orc_id' => $orc1->id, 'number' => '1', 'order' => 1]);
Davhar::create(['orc_id' => $orc1->id, 'number' => '2', 'order' => 2]);
// ... more davhars

// Door numbers will be automatically calculated and assigned
// Orc 1: doors 1-96, Orc 2: doors 97-192
```

### Type 2: Orc-wise Consecutive Numbering

```php
// Create Korpus with Type 2 numbering
$korpus = Korpus::create([
    'bair_id' => $bair->id,
    'name' => 'Block B',
    'numbering_type' => 2
]);

// Each Orc will have independent numbering starting from 1
```

### Type 3: Floor-wise Consecutive Numbering

```php
// Create Korpus with Type 3 numbering
$korpus = Korpus::create([
    'bair_id' => $bair->id,
    'name' => 'Block C',
    'numbering_type' => 3,
    'digit_multiplier' => 100 // 3-digit format
]);

// Create Davhars with floor numbers
Davhar::create([
    'orc_id' => $orc->id,
    'number' => '1',
    'order' => 1,
    'floor_number' => 1
]);
// Floor 1 doors: 101-106
```

### Manual Door Number Override

```php
use App\Services\DoorNumbering\ManualDoorNumberingService;

$manualService = app(ManualDoorNumberingService::class);

// Set custom door numbers for a Davhar
$result = $manualService->setDavharDoorNumbers(
    $davhar,
    [101, 103, 105, 107, 109, 111] // Custom sequence
);

// Update a single door number
$result = $manualService->setSingleDoorNumber(
    $korpus,
    $davhar,
    101, // old number
    201  // new number
);
```

## Automatic Triggers

The system automatically recalculates door numbers when:

1. **Korpus updated** with relevant field changes:
   - `numbering_type`
   - `digit_multiplier`
   - `begin_toot_number`
   - `end_toot_number`

2. **Davhar created** in an existing Korpus

3. **Davhar updated** with `floor_number` changes (Type 3)

4. **Davhar deleted** from a Korpus

## Validation Rules

### Type 1 Validation
- Begin and end toot numbers must be set
- Begin must be less than end
- At least one Orc must exist
- All Orcs must have Davhars
- Total doors must be at least equal to number of Orcs

### Type 2 Validation
- At least one Orc must exist
- All Orcs must have Davhars
- No Korpus-level door range required

### Type 3 Validation
- Digit multiplier must be 10, 100, 1000, or 10000
- At least one Orc must exist
- All Orcs must have Davhars
- Floor numbers must be unique within each Orc
- Door sequence must not exceed digit capacity

## Error Handling

The system includes comprehensive error handling:

- **Service Unavailability**: Graceful degradation when services are down
- **Validation Errors**: Clear error messages for configuration issues
- **Database Errors**: Transaction rollback on failures
- **Logging**: Detailed logging for debugging and monitoring

## Testing

Run the comprehensive test suite:

```bash
php artisan test tests/Feature/DoorNumberingTest.php
```

The tests cover:
- All three numbering types
- Validation scenarios
- Statistics calculation
- Error conditions

## Migration

To apply the door numbering system to existing data:

1. Run the migrations:
```bash
php artisan migrate
```

2. Update existing Korpus records with numbering types:
```php
// Set default numbering type for existing Korpus
Korpus::whereNull('numbering_type')->update(['numbering_type' => 1]);
```

3. Trigger door number calculation for existing Korpus:
```php
use App\Services\DoorNumbering\DoorNumberingService;

$service = app(DoorNumberingService::class);
foreach (Korpus::all() as $korpus) {
    $service->calculateKorpusDoorNumbers($korpus);
}
```

## Performance Considerations

- Door number calculation is performed in database transactions
- Bulk operations are used for creating multiple doors
- Observer events are optimized to avoid unnecessary recalculations
- Validation is performed before expensive operations

## Future Enhancements

The system is designed to support additional features:

- **Type 4**: Building-wide consecutive numbering
- **Type 5**: Custom numbering schemes
- **Bulk import/export**: CSV import/export of door configurations
- **Audit trail**: Track changes to door numbers
- **Advanced validation**: Cross-building validation rules
