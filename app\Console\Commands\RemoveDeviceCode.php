<?php

namespace App\Console\Commands;

use App\Services\ErkhService;
use Illuminate\Console\Command;

class RemoveDeviceCode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:remove-device-code';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    public $erkhService;

    public function __construct(ErkhService $erkhService) {
        parent::__construct();
        $this->erkhService = $erkhService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->erkhService->removeDeviceCodes();
    }
}
