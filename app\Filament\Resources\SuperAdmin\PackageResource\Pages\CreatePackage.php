<?php

namespace App\Filament\Resources\SuperAdmin\PackageResource\Pages;

use App\Services\PackageService;
use App\Filament\Resources\SuperAdmin\PackageResource;
use Filament\Resources\Pages\CreateRecord;

class CreatePackage extends CreateRecord
{
    protected static string $resource = PackageResource::class;

    protected function afterCreate(): void
    {
        $record = $this->record;
        $data   = $this->data;
        if (!$record->is_new_os_erkh)
            return;
        $service = resolve(PackageService::class);
        $service->setNewOsErkhForPackage($data['sukhs'], $record->id);
    }
}
