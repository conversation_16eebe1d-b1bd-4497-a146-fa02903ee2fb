<?php

namespace App\Http\Controllers;

use App\Http\Requests\GetPackagesRequest;
use App\Http\Requests\GetTotalPriceRequest;
use App\Http\Resources\Package as PackageResource;
use App\Http\Resources\TotalPrice as TotalPriceResource;
use App\Models\Package;
use App\Services\PackageService;

class PackageController extends Controller
{

    /**
     * Орцнуудын мэдээлэлийг нэвтрэх эрхтэй нь хамт авах жагсаалт.
     *
     * Тухайн оршин суугчийн хамаарал бүхий орцнуудын мэдээлэлийг авах зориулалттай.
     */
    public function index(GetPackagesRequest $request)
    {
        $limit    = $request->input(GetPackagesRequest::LIMIT, 10);
        $packages = Package::query();

        $sort = $request->input(GetPackagesRequest::SORT);
        if (isset($sort[GetPackagesRequest::SORT_FIELD])) {
            $sortField = $sort[GetPackagesRequest::SORT_FIELD];
            $sortType  = $sort[GetPackagesRequest::SORT_TYPE];
            $packages  = $packages->orderBy($sortField, $sortType);
        }

        $packages = $packages->paginate($limit);

        return PackageResource::collection($packages);
    }

    /**
     * Худалдан авалтын үнэ авах.
     *
     * Оршин суугчийн сонгосон багц, сар, гишүүдийн тооноос хамааран үнэ тооцоолон авах зориулалттай.
     */
    public function getTotalPrice(GetTotalPriceRequest $request)
    {
        $packageId   = $request->input(GetTotalPriceRequest::PARAMETER_PACKAGE_ID);
        $month       = $request->input(GetTotalPriceRequest::PARAMETER_MONTH);
        $memberCount = $request->input(GetTotalPriceRequest::PARAMETER_MEMBER_COUNT);

        $service    = resolve(PackageService::class);
        $totalPrice = $service->getTotalPrice($packageId, $month, $memberCount);
        return new TotalPriceResource((object) $totalPrice);
    }
}
