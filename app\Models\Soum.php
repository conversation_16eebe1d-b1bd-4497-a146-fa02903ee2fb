<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Soum
 *
 * @mixin IdeHelperSoum
 * @property int $id
 * @property string $name
 * @property int $aimag_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Aimag $aimag
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Bag> $bags
 * @property-read int|null $bags_count
 * @method static \Illuminate\Database\Eloquent\Builder|Soum newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Soum newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Soum query()
 * @method static \Illuminate\Database\Eloquent\Builder|Soum whereAimagId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Soum whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Soum whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Soum whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Soum whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Soum extends Model
{
    use HasFactory;

    const ID                   = 'id';
    const AIMAG_ID             = 'aimag_id';
    const NAME                 = 'name';

    const RELATION_BAGS        = 'bags';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::AIMAG_ID,
        self::NAME,
    ];

    public function aimag()
    {
        return $this->belongsTo(Aimag::class);
    }

    public function bags()
    {
        return $this->hasMany(Bag::class);
    }
}
