<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\SBUserResource\Pages;
use App\Filament\Resources\SuperAdmin\SBUserResource\RelationManagers;
use App\Models\SBUser;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SBUserResource extends Resource
{
    protected static ?string $model = SBUser::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Хэрэглэгч';
    protected static ?string $modelLabel = 'Хэрэглэгч';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'sb-users';
    protected static ?string $navigationGroup = 'Төрийн банк';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make(SBUser::USERNAME)
                    ->label('Нэвтрэх нэр')
                    ->maxLength(30)
                    ->required(),
                Forms\Components\TextInput::make(SBUser::HINT)
                    ->label('hint')
                    ->password()
                    ->revealable()
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make(SBUser::USERNAME)->label('Нэр'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->groupedBulkActions([])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-o-plus'),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSBUsers::route('/'),
            'create' => Pages\CreateSBUser::route('/create'),
            'view' => Pages\ViewSBUser::route('/{record}'),
        ];
    }
}
