<?php

namespace App\Services;

use App\Models\Sukh;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

class SukhSyncService
{
    protected CvSecurityServiceExt $cvSecurityService;

    public function __construct(CvSecurityServiceExt $cvSecurityService)
    {
        $this->cvSecurityService = $cvSecurityService;
    }

    /**
     * Sync Sukh creation with CVSecurity service
     *
     * @param Sukh $sukh
     * @return void
     */
    public function syncCreate(Sukh $sukh): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Sukh creation', [
                    'sukh_id' => $sukh->id,
                    'sukh_name' => $sukh->name
                ]);
                $this->updateSukhCode($sukh, null);
                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareSukhData($sukh);

            // Call CVSecurity create endpoint
            $response = $this->cvSecurityService->createSukh($data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateSukhCode($sukh, $code);
                Log::info('Sukh successfully synced with CVSecurity on creation', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $code
                ]);
            } else {
                $this->updateSukhCode($sukh, null);
                Log::error('CVSecurity create operation failed for Sukh', [
                    'sukh_id' => $sukh->id,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            $this->updateSukhCode($sukh, null);
            Log::error('Exception during Sukh CVSecurity sync on creation', [
                'sukh_id' => $sukh->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync Sukh update with CVSecurity service
     *
     * @param Sukh $sukh
     * @return void
     */
    public function syncUpdate(Sukh $sukh): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Sukh update', [
                    'sukh_id' => $sukh->id,
                    'sukh_name' => $sukh->name
                ]);
                return;
            }

            // If no code exists, treat as create operation
            if (!$sukh->code) {
                $this->syncCreate($sukh);
                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareSukhData($sukh);

            // Call CVSecurity update endpoint
            $response = $this->cvSecurityService->updateSukh($sukh->code, $data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateSukhCode($sukh, $code);
                Log::info('Sukh successfully synced with CVSecurity on update', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $code
                ]);
            } else {
                Log::error('CVSecurity update operation failed for Sukh', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $sukh->code,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Sukh CVSecurity sync on update', [
                'sukh_id' => $sukh->id,
                'cv_code' => $sukh->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync Sukh deletion with CVSecurity service
     *
     * @param Sukh $sukh
     * @return void
     */
    public function syncDelete(Sukh $sukh): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Sukh deletion', [
                    'sukh_id' => $sukh->id,
                    'sukh_name' => $sukh->name
                ]);
                return;
            }

            // If no code exists, nothing to delete in CVSecurity
            if (!$sukh->code) {
                Log::info('No CVSecurity code found for Sukh deletion', [
                    'sukh_id' => $sukh->id
                ]);
                return;
            }

            // Call CVSecurity delete endpoint
            $success = $this->cvSecurityService->deleteSukh($sukh->code);

            if ($success) {
                Log::info('Sukh successfully deleted from CVSecurity', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $sukh->code
                ]);
            } else {
                Log::error('CVSecurity delete operation failed for Sukh', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $sukh->code
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Sukh CVSecurity sync on deletion', [
                'sukh_id' => $sukh->id,
                'cv_code' => $sukh->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Prepare Sukh data for CVSecurity API
     *
     * @param Sukh $sukh
     * @return array
     */
    protected function prepareSukhData(Sukh $sukh): array
    {
        return [
            'name' => $sukh->name,
        ];
    }

    /**
     * Extract code from CVSecurity response
     *
     * @param object|null $response
     * @return string|null
     */
    protected function extractCodeFromResponse(?object $response): ?string
    {
        if (!$response) {
            return null;
        }

        // Try to extract code from different possible response structures
        if (isset($response->code)) {
            return $response->code;
        }

        if (isset($response->area->code)) {
            return $response->area->code;
        }

        if (isset($response->department->code)) {
            return $response->department->code;
        }

        return null;
    }

    /**
     * Update Sukh code field without triggering observers
     *
     * @param Sukh $sukh
     * @param string|null $code
     * @return void
     */
    protected function updateSukhCode(Sukh $sukh, ?string $code): void
    {
        // Use updateQuietly to avoid triggering observers again
        $sukh->updateQuietly([Sukh::CODE => $code]);
    }

    /**
     * Sync existing Sukh with CVSecurity (for read operations)
     *
     * @param Sukh $sukh
     * @return void
     */
    public function syncRead(Sukh $sukh): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Sukh read sync', [
                    'sukh_id' => $sukh->id
                ]);
                return;
            }

            // If no code exists, nothing to read from CVSecurity
            if (!$sukh->code) {
                Log::info('No CVSecurity code found for Sukh read sync', [
                    'sukh_id' => $sukh->id
                ]);
                return;
            }

            // Call CVSecurity read endpoint
            $response = $this->cvSecurityService->getSukhByCode($sukh->code);

            if ($response) {
                Log::info('Sukh data successfully retrieved from CVSecurity', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $sukh->code
                ]);
            } else {
                Log::warning('CVSecurity read operation returned no data for Sukh', [
                    'sukh_id' => $sukh->id,
                    'cv_code' => $sukh->code
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Sukh CVSecurity sync on read', [
                'sukh_id' => $sukh->id,
                'cv_code' => $sukh->code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
