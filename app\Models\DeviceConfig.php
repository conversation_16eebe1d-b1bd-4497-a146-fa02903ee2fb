<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\DeviceConfig
 *
 * @mixin IdeHelperDeviceConfig
 * @property int $id
 * @property string $name
 * @property string $pro_id
 * @property int $qty
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereProId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereQty($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceConfig whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DeviceConfig extends Model
{
    use HasFactory;

    const ID          = 'id';
    const NAME        = 'name';
    const PRO_ID      = 'pro_id';
    const QTY         = 'qty';
    const IS_ACTIVE   = 'is_active';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
        self::PRO_ID,
        self::QTY,
        self::IS_ACTIVE,
    ];
}
