<?php

namespace App\Filament\Resources\SuperAdmin\AimagResource\Pages;

use App\Filament\Resources\SuperAdmin\AimagResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAimags extends ListRecords
{
    protected static string $resource = AimagResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
        ];
    }
}
