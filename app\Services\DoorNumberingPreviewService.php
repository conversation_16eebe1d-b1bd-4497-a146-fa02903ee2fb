<?php

namespace App\Services;

/**
 * Service for generating door numbering previews without persisting to database
 *
 * This service provides real-time preview functionality for the door numbering system,
 * showing how floors (Davhars) and doors (Toots) will be generated based on configuration
 * parameters without actually creating database records.
 */
class DoorNumberingPreviewService
{
    /**
     * Generate preview data for door numbering configuration
     *
     * @param array $config Configuration parameters
     * @return array Preview data structure
     */
    public function generatePreview(array $config): array
    {
        try {
            $numberingType = $config['numbering_type'] ?? null;
            $digitMultiplier = $config['digit_multiplier'] ?? 100;
            $floorsCount = $config['floors_count'] ?? 1;
            $doorsPerFloor = $config['doors_per_floor'] ?? 6;
            $orcs = $config['orcs'] ?? [];

            if (!$numberingType) {
                return ['empty' => true];
            }

            // Validate configuration
            if ($floorsCount < 1 || $doorsPerFloor < 1) {
                return ['error' => 'Давхрын тоо болон хаалганы тоо 1-ээс их байх ёстой'];
            }

            if (empty($orcs)) {
                return ['error' => 'Орц тодорхойлогдоогүй байна'];
            }

            // Generate preview based on numbering type
            switch ($numberingType) {
                case 1:
                    return $this->generateType1Preview($orcs, $floorsCount, $doorsPerFloor, $digitMultiplier);
                case 2:
                    return $this->generateType2Preview($orcs, $floorsCount, $doorsPerFloor, $digitMultiplier);
                case 3:
                    return $this->generateType3Preview($orcs, $floorsCount, $doorsPerFloor, $digitMultiplier);
                default:
                    return ['error' => 'Дэмжигдээгүй дугаарлалтын төрөл: ' . $numberingType];
            }
        } catch (\Exception $e) {
            return ['error' => 'Урьдчилан харахад алдаа гарлаа: ' . $e->getMessage()];
        }
    }

    /**
     * Generate Type 1 preview: Korpus-wise consecutive numbering
     */
    protected function generateType1Preview(array $orcs, int $floorsCount, int $doorsPerFloor, int $digitMultiplier): array
    {
        $previewOrcs = [];
        $totalDoors = 0;
        $currentDoorNumber = 1;

        foreach ($orcs as $orcConfig) {
            $orcNumber = $orcConfig['number'] ?? 1;
            $orcFloorsCount = $orcConfig['floors_count_override'] ?? $floorsCount;

            $floors = [];
            for ($floorNum = 1; $floorNum <= $orcFloorsCount; $floorNum++) {
                $startDoor = $currentDoorNumber;
                $endDoor = $currentDoorNumber + $doorsPerFloor - 1;

                $floors[] = [
                    'number' => $floorNum,
                    'door_range' => $startDoor . '-' . $endDoor,
                    'door_count' => $doorsPerFloor
                ];

                $currentDoorNumber += $doorsPerFloor;
                $totalDoors += $doorsPerFloor;
            }

            $previewOrcs[] = [
                'number' => $orcNumber,
                'floors_count' => $orcFloorsCount,
                'floors' => $floors,
                'total_doors' => $orcFloorsCount * $doorsPerFloor
            ];
        }

        return [
            'type' => 1,
            'type_name' => 'Төрөл 1: Блокийн дугаарлалт',
            'description' => 'Бүх блокийн хаалганууд дараалан дугаарлагдана',
            'orcs' => $previewOrcs,
            'total_doors' => $totalDoors,
            'digit_multiplier' => $digitMultiplier
        ];
    }

    /**
     * Generate Type 2 preview: Orc-wise consecutive numbering
     */
    protected function generateType2Preview(array $orcs, int $floorsCount, int $doorsPerFloor, int $digitMultiplier): array
    {
        $previewOrcs = [];
        $totalDoors = 0;

        foreach ($orcs as $orcConfig) {
            $orcNumber = $orcConfig['number'] ?? 1;
            $orcFloorsCount = $orcConfig['floors_count_override'] ?? $floorsCount;

            $floors = [];
            $currentDoorNumber = 1; // Reset for each Orc

            for ($floorNum = 1; $floorNum <= $orcFloorsCount; $floorNum++) {
                $startDoor = $currentDoorNumber;
                $endDoor = $currentDoorNumber + $doorsPerFloor - 1;

                $floors[] = [
                    'number' => $floorNum,
                    'door_range' => $startDoor . '-' . $endDoor,
                    'door_count' => $doorsPerFloor
                ];

                $currentDoorNumber += $doorsPerFloor;
            }

            $orcTotalDoors = $orcFloorsCount * $doorsPerFloor;
            $totalDoors += $orcTotalDoors;

            $previewOrcs[] = [
                'number' => $orcNumber,
                'floors_count' => $orcFloorsCount,
                'floors' => $floors,
                'total_doors' => $orcTotalDoors
            ];
        }

        return [
            'type' => 2,
            'type_name' => 'Төрөл 2: Орцны дугаарлалт',
            'description' => 'Орц бүрийн хаалганууд 1-ээс эхлэн дугаарлагдана',
            'orcs' => $previewOrcs,
            'total_doors' => $totalDoors,
            'digit_multiplier' => $digitMultiplier
        ];
    }

    /**
     * Generate Type 3 preview: Floor-wise consecutive numbering
     */
    protected function generateType3Preview(array $orcs, int $floorsCount, int $doorsPerFloor, int $digitMultiplier): array
    {
        $previewOrcs = [];
        $totalDoors = 0;

        foreach ($orcs as $orcConfig) {
            $orcNumber = $orcConfig['number'] ?? 1;
            $orcFloorsCount = $orcConfig['floors_count_override'] ?? $floorsCount;

            $floors = [];

            for ($floorNum = 1; $floorNum <= $orcFloorsCount; $floorNum++) {
                // Type 3 calculation: (floor_number * digit_multiplier) + door_sequence
                $startDoor = ($floorNum * $digitMultiplier) + 1;
                $endDoor = ($floorNum * $digitMultiplier) + $doorsPerFloor;

                $floors[] = [
                    'number' => $floorNum,
                    'door_range' => $startDoor . '-' . $endDoor,
                    'door_count' => $doorsPerFloor
                ];
            }

            $orcTotalDoors = $orcFloorsCount * $doorsPerFloor;
            $totalDoors += $orcTotalDoors;

            $previewOrcs[] = [
                'number' => $orcNumber,
                'floors_count' => $orcFloorsCount,
                'floors' => $floors,
                'total_doors' => $orcTotalDoors
            ];
        }

        return [
            'type' => 3,
            'type_name' => 'Төрөл 3: Давхрын дугаарлалт',
            'description' => 'Хаалганы дугаарт давхрын дугаар орно',
            'orcs' => $previewOrcs,
            'total_doors' => $totalDoors,
            'digit_multiplier' => $digitMultiplier
        ];
    }
}
