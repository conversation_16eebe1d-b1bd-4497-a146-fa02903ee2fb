<?php

namespace App\Models\Constant;

class Message
{
    private static $authenticationMessages = [
        1 => 'Таны СӨХ бүртгэлийг тань хийгээгүй байна. Та нэвтрэх нэрээ шалгаж үзэх эсвэл өөрийнхөө СӨХ-д хандана уу!',
        2 => 'Таны нэвтрэх нэр эсвэл нууц үг тохирохгүй байна.',
        3 => 'Идэвхжүүлсэн кодны хугацаа дууссан байна.',
        4 => 'Оршин суугч биш байна.',
    ];

    private static $systemMessages = [
        5 => 'Хэрэглэгч олдсонгүй.',
        6 => 'SmsToken олдсонгүй. ',
        7 => 'SmsToken тохирохгүй байна.',
        8 => 'Хэрэглэгч идэвхгүй төлөвтэй байна.',
        9 => 'Утасны дугаар бүртгэгдээгүй байна.',
        10 => 'Утасны дугаар бүртгэгдсэн байна.',
        11 => 'Эрх үүссэн байна. Дахин эрх үүсгэх шаардлагагүй.',
        12 => 'Бүртгэлгүй төхөөрөмж байна. ',
        13 => 'Эрхтэй байр эсвэл орц биш байна. ',
        14 => 'Энэ орцны төхөөрөмж олдсонгүй байна. ',
        15 => 'Эрхтэй орц биш байна. ',
    ];

    private static $qpayMessage = [
        1 => 'Qpay-с токен авах үед алдаа гарлаа.',
        2 => 'Гүйлгээ олдсонгүй.',
        3 => 'Гүйлгээний дүн дор хаяж 100 төгрөгнөөс дээш байх ёстой',
    ];

    private static $bpayMessage = [
        1 => 'Bpay-с токен авах үед алдаа гарлаа.',
        2 => 'Bpay-с нэхэмжлэх татах үед алдаа гарлаа.',
        3 => 'Bpay-р төлбөр төлөх явцад алдаа гарлаа.',
        4 => 'Bpay-р төлбөр шалгах явцад алдаа гарлаа.',
        5 => 'Bpay-н сервис дуудах явцад алдаа гарлаа.',
        6 => 'callApi-г дуудах явцад алдаа гарлаа.',
        7 => 'Оршин суугчийн дахин давтагдашгүй код тохируулагдаагүй байна',
    ];

    private static $orshinSuugchMessage = [
        1 => 'Оршин суугчийн тоот олдсонгүй.',
        2 => 'Таны код өөрчлөх хүсэлт өмнөх хүсэлт хийгдсэнээс хойш 24н цагийн дараа идэвхэжнэ.',
        3 => 'Гэр бүлийн гишүүний утасны дугаар биш байна.',
    ];

    private static $erkhMessage = [
        1 => 'Энэ багц ашиглагдсан байна.',
        2 => 'Эрх олдсонгүй. Та эрхээ сунгана уу!',
    ];

    private static $infoMessages = [
        1 => '',
    ];

    public static function getAuthMessage($key) {
        return self::$authenticationMessages[$key];
    }

    public static function getSystemMessage($key) {
        return self::$systemMessages[$key];
    }

    public static function getInfoMessage($key) {
        return self::$infoMessages[$key];
    }

    public static function getQpayMessage($key) {
        return self::$qpayMessage[$key];
    }

    public static function getBpayMessage($key) {
        return self::$bpayMessage[$key];
    }

    public static function getOrshinSuugchMessage($key) {
        return self::$orshinSuugchMessage[$key];
    }

    public static function getErkhMessage($key) {
        return self::$erkhMessage[$key];
    }
}
