<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\PackageMember
 *
 * @mixin IdeHelperPackageMember
 * @property int $id
 * @property int $package_id
 * @property int $value
 * @property float $discount
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Package $package
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember query()
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember whereDiscount($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember wherePackageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|PackageMember whereValue($value)
 * @mixin \Eloquent
 */
class PackageMember extends Model
{
    use HasFactory;

    const ID              = 'id';
    const PACKAGE_ID      = 'package_id';
    const VALUE           = 'value';
    const DISCOUNT        = 'discount';

    const RELATION_PACKAGE  = 'package';

    protected $fillable = [
        self::ID,
        self::PACKAGE_ID,
        self::VALUE,
        self::DISCOUNT,
    ];

    public function package(): BelongsTo
    {
        return $this->belongsTo(Package::class);
    }
}
