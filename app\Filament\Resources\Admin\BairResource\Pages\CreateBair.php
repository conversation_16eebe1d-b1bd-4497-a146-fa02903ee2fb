<?php

namespace App\Filament\Resources\Admin\BairResource\Pages;

use App\Services\UserInfoService;
use App\Models\Orc;
use App\Filament\Resources\Admin\BairResource;
use App\Models\Bair;
use App\Models\Korpus;
use Filament\Resources\Pages\CreateRecord;

class CreateBair extends CreateRecord
{
    protected static string $resource = BairResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        $data[Bair::SUKH_ID] = $sukh->id;
        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        $korpus = new Korpus([ Korpus::BAIR_ID => $record->id, Korpus::NAME => 'A', Korpus::ORDER => 1]);
        $korpus->save();
        $orc = new Orc([Orc::KORPUS_ID => $korpus->id, Orc::NUMBER => 1]);
        $korpus->orcs()->save($orc);
    }
}
