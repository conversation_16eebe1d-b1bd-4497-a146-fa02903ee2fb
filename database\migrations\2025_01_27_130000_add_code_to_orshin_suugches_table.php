<?php

use App\Models\OrshinSuugch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orshin_suugches', function (Blueprint $table) {
            $table->string(OrshinSuugch::CODE, 30)->nullable()->after(OrshinSuugch::UNIQ_CODE);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orshin_suugches', function (Blueprint $table) {
            $table->dropColumn(OrshinSuugch::CODE);
        });
    }
};
