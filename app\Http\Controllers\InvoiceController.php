<?php

namespace App\Http\Controllers;

use App\Enums\CQPEnum;
use App\Enums\ProductEnum;
use App\Models\Constant\ConstData;
use App\Models\Invoice;
use App\Http\Requests\CreateQpayInvoiceRequest;
use App\Http\Requests\ChangeInvoiceStatusRequest;
use App\Http\Resources\Invoice as InvoiceResource;
use App\Http\Resources\CreateInvoice as CreateInvoiceResource;
use App\Services\OrshinSuugchService;
use App\Services\InvoiceService;
use App\Services\DeviceService;
use App\Services\ErkhService;
use App\Services\ChirpStackService;
use App\Exceptions\SystemException;
use Illuminate\Support\Facades\DB;

class InvoiceController extends Controller
{
    /**
     * Багц худалдан авах.
     *
     * Оршин суугч өөрт тохирсон багцаа сонгон худалдан авах үед ашиглах зориулалттай.
     */
    public function createQpayInvoice(CreateQpayInvoiceRequest $request) {
        try {
            DB::beginTransaction();
            $packageId    = $request->input(CreateQpayInvoiceRequest::PARAMETER_PACKAGE_ID);
            $korpusId     = $request->input(CreateQpayInvoiceRequest::PARAMETER_KORPUS_ID);
            $number       = $request->input(CreateQpayInvoiceRequest::PARAMETER_NUMBER);
            $month        = $request->input(CreateQpayInvoiceRequest::PARAMETER_MONTH);
            $pMembers     = $request->input(CreateQpayInvoiceRequest::PARAMETER_MEMBERS, []);

            $user         = auth()->user();
            $oshService   = resolve(OrshinSuugchService::class);
            $orshinSuugch = $oshService->getOrshinSuugchByPhone($user->phone);

            if (!isset($orshinSuugch))
                throw new SystemException(ConstData::AUTH_EXCEPTION, 4);

            $memberIds = [];
            foreach ($pMembers as $key => $phone) {
                $foundMember = $orshinSuugch->members->first(function ($member) use ($phone) {
                    return $member->phone == $phone;
                });
                if (!isset($foundMember))
                    throw new SystemException(ConstData::ORSHIN_SUUGCH_EXCEPTION, 3);
                $memberIds[] = $foundMember->id;
            }

            $service = resolve(InvoiceService::class);
            $invoice = $service->createQpayInvoice($orshinSuugch->id, $korpusId, $number, $packageId, $month, $memberIds);
            DB::commit();
            return new CreateInvoiceResource($invoice);
        } catch (\Throwable $th) {
            DB::rollback();
            throw $th;
        }
    }

    public function changeInvoiceStatus(ChangeInvoiceStatusRequest $request) {
        $orshinSuugchId = $request->input(ChangeInvoiceStatusRequest::OS);
        $invoiceNo      = $request->input(ChangeInvoiceStatusRequest::NO);
        $invoiceService = resolve(InvoiceService::class);
        $invoice        = $invoiceService->changeInvoiceStatus($orshinSuugchId, $invoiceNo);
        $erkhService    = resolve(ErkhService::class);
        $erkhService->setErkh($orshinSuugchId, $invoice->korpus_id, $invoice->number, $invoice->id, $invoice->package_id, $invoice->valid_day);

        foreach ($invoice->invoice_members as $key => $invoiceMember) {
            $erkhService->setErkh($invoiceMember->orshin_suugch_id, $invoice->korpus_id, $invoice->number, $invoice->id, $invoice->package_id, $invoice->valid_day);
        }
        $deviceService = resolve(DeviceService::class);
        $devEui        = $deviceService->getDevEuiOs($orshinSuugchId, $invoice->korpus_id, $invoice->number);
        if (!isset($devEui))
            throw new SystemException(ConstData::SYSTEM_EXCEPTION, 15);

        foreach ($invoice->package->products as $product) {
            switch ($product) {
                case ProductEnum::KEY:
                    $deviceCode   = $invoice->orshin_suugch->device_code;
                    $data         = base64_encode("1;$deviceCode");
                    $chirpService = resolve(ChirpStackService::class);
                    $chirpService->postDeviceQueue($devEui, $data, CQPEnum::GATE);
                    foreach ($invoice->invoice_members as $key => $invoiceMember) {
                        $memberDeviceCode = $invoiceMember->orshin_suugch->device_code;
                        $memberData = base64_encode("1;$memberDeviceCode");
                        $chirpService->postDeviceQueue($devEui, $memberData, CQPEnum::GATE);
                    }
                    break;
            }
        }
    }

    public function show(Invoice $id)
    {
        return new InvoiceResource($id);
    }
}
