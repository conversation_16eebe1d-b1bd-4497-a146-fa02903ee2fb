<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ChangeInvoiceStatusRequest extends FormRequest
{
    const OS = 'os';
    const NO = 'no';
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'os' => 'required|exists:orshin_suugches,id',
            'no' => 'required|exists:invoices,sender_invoice_no',
        ];
    }
}
