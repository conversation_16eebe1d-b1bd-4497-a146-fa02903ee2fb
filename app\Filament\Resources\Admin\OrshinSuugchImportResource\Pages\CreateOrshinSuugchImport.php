<?php

namespace App\Filament\Resources\Admin\OrshinSuugchImportResource\Pages;

use App\Jobs\ImportOs;
use App\Filament\Resources\Admin\OrshinSuugchImportResource;
use App\Models\Constant\ConstData;
use Filament\Resources\Pages\CreateRecord;

class CreateOrshinSuugchImport extends CreateRecord
{
    protected static string $resource = OrshinSuugchImportResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data[ConstData::STATUS]  = ConstData::STATUS_PROCESSING;
        $data[ConstData::MESSAGE] = ConstData::MSG_PROCESSING;
        return $data;
    }

    /**
     * Triggered after the resource is created.
     *
     * This is used to dispatch the job to import the excel file.
     *
     * @return void
     */
    protected function afterCreate(): void
    {
        try {
            $record   = $this->record;
            $filePath = array_values(($this->data)[ConstData::ATTACHMENT])[0]->path();
            ImportOs::dispatch($record->id, $filePath);
        } catch (\Throwable $th) {
            $record->status  = ConstData::STATUS_ERROR;
            $msg             = $th->getMessage();
            $msg             = strlen($msg) > 255 ? substr($msg, 0, 250) : $msg;
            $record->message = $msg;
            $record->save();
        }
    }
}
