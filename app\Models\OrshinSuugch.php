<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\OrshinSuugch
 *
 * @mixin IdeHelperOrshinSuugch
 * @property int $id
 * @property string|null $last_name
 * @property string|null $name
 * @property string $phone
 * @property string|null $email
 * @property bool $is_admin
 * @property int|null $parent_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $device_code
 * @property string|null $uniq_code
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrshinSuugch> $members
 * @property-read int|null $members_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\OrshinSuugchToot> $orshin_suugch_toots
 * @property-read int|null $orshin_suugch_toots_count
 * @property-read OrshinSuugch|null $parent_orshin_suugch
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Sukh> $sukhs
 * @property-read int|null $sukhs_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereDeviceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereIsAdmin($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereUniqCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugch whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class OrshinSuugch extends Model
{
    use HasFactory, HasApiTokens;

    const ID                           = 'id';
    const LAST_NAME                    = 'last_name';
    const NAME                         = 'name';
    const PHONE                        = 'phone';
    const EMAIL                        = 'email';
    const IS_ADMIN                     = 'is_admin';
    const PARENT_ID                    = 'parent_id';
    const DEVICE_CODE                  = 'device_code';
    const UNIQ_CODE                    = 'uniq_code';
    const CODE                         = 'code';

    const DISPLAY_NAME                 = 'display_name';

    const RELATION_ORSHIN_SUUGCH_TOOTS = 'orshin_suugch_toots';
    const RELATION_SUKHS               = 'sukhs';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::LAST_NAME,
        self::NAME,
        self::PHONE,
        self::EMAIL,
        self::IS_ADMIN,
        self::PARENT_ID,
        self::DEVICE_CODE,
        self::UNIQ_CODE,
        self::CODE,
    ];

    public function parent_orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class, 'id', 'parent_id');
    }

    public function orshin_suugch_toots(): HasMany
    {
        return $this->hasMany(OrshinSuugchToot::class);
    }

    public function toots_str()
    {
        $tootStr = '';
        foreach ($this->orshin_suugch_toots as $key => $toot) {
            $tootStr .= $toot->number;
        }
        return $tootStr;
    }

    public function members(): HasMany
    {
        return $this->hasMany(OrshinSuugch::class, 'parent_id', 'id');
    }

    public function sukhs(): BelongsToMany
    {
        return $this->belongsToMany(Sukh::class);
    }
}
