<?php

namespace App\Models;

use App\Enums\ProductEnum;
use Illuminate\Database\Eloquent\Casts\AsEnumCollection;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\Erkh
 *
 * @mixin IdeHelperErkh
 * @property int $id
 * @property int $orshin_suugch_id
 * @property int $bair_id
 * @property int $invoice_id
 * @property string $begin_date
 * @property string $end_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property AsEnumCollection|null $products
 * @property bool $removed_device_code
 * @property int|null $number
 * @property int|null $korpus_id
 * @property-read \App\Models\Invoice|null $invoice
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \App\Models\OrshinSuugch|null $orshin_suugch
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh query()
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereBairId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereBeginDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereEndDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereInvoiceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereProducts($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereRemovedDeviceCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Erkh whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Erkh extends Model
{
    use HasFactory;

    const ID                        = 'id';
    const ORSHIN_SUUGCH_ID          = 'orshin_suugch_id';
    const KORPUS_ID                 = 'korpus_id';
    const NUMBER                    = 'number';
    const INVOICE_ID                = 'invoice_id';
    const BEGIN_DATE                = 'begin_date';
    const END_DATE                  = 'end_date';
    const PRODUCTS                  = 'products';
    const REMOVED_DEVICE_CODE       = 'removed_device_code';

    const VALUE                     = 'value';

    const RELATION_INVOICE          = 'invoice';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::ORSHIN_SUUGCH_ID,
        self::KORPUS_ID,
        self::NUMBER,
        self::INVOICE_ID,
        self::BEGIN_DATE,
        self::END_DATE,
        self::PRODUCTS,
        self::REMOVED_DEVICE_CODE
    ];

    protected $casts = [
        'products' => AsEnumCollection::class . ':' . ProductEnum::class,
    ];

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }

    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }
}
