<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\DeviceConfigResource\Pages;
use App\Models\DeviceConfig;
use App\Services\ChirpStackService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class DeviceConfigResource extends Resource
{
    protected static ?string $model = DeviceConfig::class;

    protected static ?string $navigationIcon = 'heroicon-o-server';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Төхөөрөмжийн тохиргоо';
    protected static ?string $modelLabel = 'төхөөрөмж тохиргоо';
    protected static ?int $navigationSort = 4;
    protected static ?string $slug = 'deviceConfigs';
    protected static ?string $navigationGroup = 'Тохиргоо';

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Forms\Components\Section::make()
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Нэр')
                    ->maxValue(50)
                    ->required(),

                Forms\Components\Select::make('pro_id')
                    ->label('Профайл')
                    ->default(0)
                    ->options(
                        function (callable $get) {
                            $service = resolve(ChirpStackService::class);
                            $reponse = $service->getDeviceProfiles(50);
                            return $reponse;
                        }
                    ),

                Forms\Components\TextInput::make('qty')
                    ->label('Тоо ширхэг')
                    ->numeric()
                    ->inputMode('decimal')
                    ->minValue(1)
                    ->mask('99999')
                    ->maxValue(10000)
                    ->required(),

                Forms\Components\Toggle::make('is_active')
                    ->onIcon('heroicon-m-bolt')
                    ->offIcon('heroicon-m-bolt-slash')
                    ->label('Идэвхтэй эсэх')
                    ->inline(False)
                    ->default(True)

            ])
            ->columns(2)
            ->columnSpan(['lg' => fn (?DeviceConfig $record) => $record === null ? 2 : 1]),

        Forms\Components\Section::make()
            ->schema([
                Forms\Components\Placeholder::make('created_at')
                    ->label('Created at')
                    ->content(fn (DeviceConfig $record): ?string => $record->created_at?->diffForHumans()),

                Forms\Components\Placeholder::make('updated_at')
                    ->label('Last modified at')
                    ->content(fn (DeviceConfig $record): ?string => $record->updated_at?->diffForHumans()),
            ])
            ->columnSpan(['lg' => 1])
            ->hidden(fn (?DeviceConfig $record) => $record === null),
        ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('qty')->label('Тоо ширхэг')->sortable()->searchable(),
                Tables\Columns\BooleanColumn::make('is_active')->label('Идэвхтэй эсэх')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDeviceConfigs::route('/'),
            'create' => Pages\CreateDeviceConfig::route('/create'),
            'edit' => Pages\EditDeviceConfig::route('/{record}/edit'),
        ];
    }
}
