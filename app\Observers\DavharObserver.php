<?php

namespace App\Observers;

use App\Models\Davhar;
use App\Services\DavharSyncService;
use Illuminate\Support\Facades\Log;

class DavharObserver
{
    protected DavharSyncService $davharSyncService;

    public function __construct(DavharSyncService $davharSyncService)
    {
        $this->davharSyncService = $davharSyncService;
    }

    /**
     * Handle the Davhar "created" event.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function created(Davhar $davhar): void
    {
        Log::info('DavharObserver: Davhar created event triggered', [
            'davhar_id' => $davhar->id,
            'davhar_number' => $davhar->number,
            'orc_id' => $davhar->orc_id
        ]);

        // Sync with CVSecurity service
        $this->davharSyncService->syncCreate($davhar);
    }

    /**
     * Handle the Davhar "updated" event.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function updated(Davhar $davhar): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($davhar->wasChanged(Davhar::CODE) && count($davhar->getChanges()) === 1) {
            Log::debug('DavharObserver: Skipping sync for code-only update', [
                'davhar_id' => $davhar->id
            ]);
            return;
        }

        Log::info('DavharObserver: Davhar updated event triggered', [
            'davhar_id' => $davhar->id,
            'davhar_number' => $davhar->number,
            'orc_id' => $davhar->orc_id,
            'changed_fields' => array_keys($davhar->getChanges())
        ]);

        // Sync with CVSecurity service
        $this->davharSyncService->syncUpdate($davhar);
    }

    /**
     * Handle the Davhar "deleted" event.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function deleted(Davhar $davhar): void
    {
        Log::info('DavharObserver: Davhar deleted event triggered', [
            'davhar_id' => $davhar->id,
            'davhar_number' => $davhar->number,
            'orc_id' => $davhar->orc_id,
            'cv_code' => $davhar->code
        ]);

        // Sync with CVSecurity service
        $this->davharSyncService->syncDelete($davhar);
    }

    /**
     * Handle the Davhar "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param Davhar $davhar
     * @return void
     */
    public function retrieved(Davhar $davhar): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->davharSyncService->syncRead($davhar);
    }
}
