<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\DeviceDtl
 *
 * @mixin IdeHelperDeviceDtl
 * @property int $id
 * @property int $device_hdr_id
 * @property string $name
 * @property string $dev_eui
 * @property string $join_eui
 * @property string $nwk_key
 * @property int $orc_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property int|null $korpus_id
 * @property-read \App\Models\DeviceHdr $deviceHdr
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \App\Models\Orc|null $orc
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl query()
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereDevEui($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereDeviceHdrId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereJoinEui($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereNwkKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereOrcId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|DeviceDtl whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class DeviceDtl extends Model
{
    use HasFactory;

    const ID            = 'id';
    const DEVICE_HDR_ID = 'device_hdr_id';
    const KORPUS_ID     = 'korpus_id';
    const ORC_ID        = 'orc_id';
    const NAME          = 'name';
    const DEV_EUI       = 'dev_eui';
    const JOIN_EUI      = 'join_eui';
    const NWK_KEY       = 'nwk_key';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::DEVICE_HDR_ID,
        self::KORPUS_ID,
        self::ORC_ID,
        self::NAME,
        self::DEV_EUI,
        self::JOIN_EUI,
        self::NWK_KEY,
    ];

    public function deviceHdr(): BelongsTo
    {
        return $this->belongsTo(DeviceHdr::class);
    }

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }

    public function orc(): BelongsTo
    {
        return $this->belongsTo(Orc::class);
    }
}
