<?php

namespace App\Filament\Resources\SuperAdmin\ContractTemplateResource\Pages;

use App\Models\ContractTemplate;
use App\Services\StorageService;
use App\Filament\Resources\SuperAdmin\ContractTemplateResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContractTemplate extends EditRecord
{
    protected static string $resource = ContractTemplateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->after(function (): void {
                    $service = resolve(StorageService::class);
                    $service->deleteFileFromMinio($this->record);
                    $contractTemplate = ContractTemplate::find($this->record->id);
                    $contractTemplate->delete();
                }),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $data[ContractTemplate::ATTACHMENTS] = [$data[ContractTemplate::FILE_PATH]];
        return $data;
    }

    protected function afterSave(): void
    {
        $record  = $this->record;
        $service = resolve(StorageService::class);
        $service->deleteFileFromMinio($record);
        $attachments = $service->putFilesToMinio(ContractTemplate::TABLE, $record, $this->data[ContractTemplate::ATTACHMENTS]);
        $this->data[ContractTemplate::ATTACHMENTS] = $attachments;
    }
}
