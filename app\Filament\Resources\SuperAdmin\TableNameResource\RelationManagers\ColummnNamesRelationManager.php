<?php

namespace App\Filament\Resources\SuperAdmin\TableNameResource\RelationManagers;

use App\Models\Constant\ConstData;
use App\Models\TableName;
use App\Services\InfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Tables\Filters\SelectFilter;

class ColummnNamesRelationManager extends RelationManager
{
    protected static string $relationship = 'column_names';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Нэр')
                    ->maxValue(30)
                    ->disabled(),

                Forms\Components\TextInput::make('description')
                    ->label('Тайлбар')
                    ->maxValue(30)
                    ->required(),

                Forms\Components\TextInput::make('value')
                    ->label('Утга')
                    ->disabled(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->label('Нэр'),
                Tables\Columns\TextColumn::make('description')->label('Тайлбар')->searchable(),
                Tables\Columns\TextColumn::make('value')->label('Утга')->copyable(),
            ])
            ->filters([
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
            ])
            ->emptyStateActions([
            ]);
    }
}
