<?php

use App\Models\Korpus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->string(Korpus::CODE, 30)->nullable()->after(Korpus::END_TOOT_NUMBER);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('korpuses', function (Blueprint $table) {
            $table->dropColumn(Korpus::CODE);
        });
    }
};
