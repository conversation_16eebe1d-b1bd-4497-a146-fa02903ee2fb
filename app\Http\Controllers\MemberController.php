<?php

namespace App\Http\Controllers;

use App\Models\Constant\ConstData;
use App\Models\OrshinSuugch;
use App\Models\OrshinSuugchToot;
use App\Models\User;
use App\Http\Requests\CreateMemberRequest;
use App\Http\Requests\UpdateMemberRequest;
use App\Http\Resources\Member as MemberResource;
use App\Http\Resources\Members as MembersResource;
use App\Services\OrshinSuugchService;
use App\Exceptions\SystemException;

class MemberController extends Controller
{
    public function getAuthOrshinSuugch(): OrshinSuugch
    {
        /** @var User $user */
        $user = auth()->user();

        $oshService   = resolve(OrshinSuugchService::class);
        $orshinSuugch = $oshService->getOrshinSuugchByPhone($user->phone);

        if (!isset($orshinSuugch)) {
            throw new SystemException(ConstData::AUTH_EXCEPTION, 4);
        }

        return $orshinSuugch;
    }

    /**
     * Өрхийн гишүүдийн жагсаалт.
     *
     * Тухайн оршин суугчийн Өрхийн гишүүдийн мэдээлэлийг авах зориулалттай.
     */
    public function index()
    {
        $orshinSuugch = $this->getAuthOrshinSuugch();
        $members      = new OrshinSuugch();
        $members      = $members->where(OrshinSuugch::PARENT_ID, $orshinSuugch->id)->get();
        return MembersResource::collection($members);
    }

    /**
     * Алхам 1. Гэр бүлийн гишүүн нэмэх.
     *
     * Оршин суугч системд өөрийн гэр бүлийн гишүүнийг утасны дугаараа нь нэмэх боломжтой.
     */
    public function store(CreateMemberRequest $request)
    {
        $lastName            = $request->input(CreateMemberRequest::PARAMETER_LAST_NAME);
        $firstName           = $request->input(CreateMemberRequest::PARAMETER_FIRST_NAME);
        $phone               = $request->input(CreateMemberRequest::PARAMETER_PHONE);
        $orshinSuugchTootIds = $request->input(CreateMemberRequest::PARAMETER_ORSHIN_SUUGCH_TOOT_IDS);

        $orshinSuugch = $this->getAuthOrshinSuugch();
        $member       = OrshinSuugch::create([
            OrshinSuugch::LAST_NAME => $lastName,
            OrshinSuugch::NAME      => $firstName,
            OrshinSuugch::PHONE     => $phone,
            OrshinSuugch::IS_ADMIN  => false,
            OrshinSuugch::PARENT_ID => $orshinSuugch->id,
        ]);

        foreach ($orshinSuugch->sukhs as $key => $orshinSuugchSukh) {
            $member->sukhs()->save($orshinSuugchSukh);
        }

        foreach ($orshinSuugchTootIds as $key => $orshinSuugchTootId) {
            $orshinSuugchToot = OrshinSuugchToot::find($orshinSuugchTootId);
            $member->orshin_suugch_toots()->create([
                OrshinSuugchToot::KORPUS_ID => $orshinSuugchToot->korpus_id,
                OrshinSuugchToot::NUMBER    => $orshinSuugchToot->number
            ]);
        }
        return new MemberResource($member);
    }

    /**
     * Алхам 1. Гэр бүлийн гишүүний мэдээлэлтийг харах.
     *
     * Оршин суугч системээс өөрийн гэр бүлийн гишүүдийн мэдээлэлийг нэг бүрчлэн харах боломжтой.
     */
    public function show(string $id)
    {
        $orshinSuugch = $this->getAuthOrshinSuugch();
        $member       = OrshinSuugch::where(OrshinSuugch::ID, $id)->where(OrshinSuugch::PARENT_ID, $orshinSuugch->id)->first();
        return $member ? new MemberResource($member) : null;
    }

    /**
     * Алхам 1. Гэр бүлийн гишүүний мэдээлэлтийг өөрчлөх боломжтой.
     *
     * Оршин суугч системээс өөрийн гэр бүлийн гишүүдийн мэдээлэлийг өөрчлөх боломжтой.
     */
    public function update(UpdateMemberRequest $request, string $id)
    {
        $lastName     = $request->input(UpdateMemberRequest::PARAMETER_LAST_NAME);
        $firstName    = $request->input(UpdateMemberRequest::PARAMETER_FIRST_NAME);
        $orshinSuugch = $this->getAuthOrshinSuugch();
        $member       = OrshinSuugch::where(OrshinSuugch::ID, $id)->where(OrshinSuugch::PARENT_ID, $orshinSuugch->id)->first();
        if ($lastName)
            $member->last_name = $lastName;
        if ($firstName)
            $member->name = $firstName;
        $member->save();
        return $member ? new MemberResource($member) : null;
    }

    /**
     * Алхам 1. Гэр бүлийн гишүүнийг хасах боломжтой.
     *
     * Оршин суугч системээс өөрийн гэр бүлийн гишүүдийг хасах боломжтой.
     *
     * @param int $member
     * @return void
     * @throws SystemException
     */
    public function destroy(int $member)
    {
        $orshinSuugch = $this->getAuthOrshinSuugch();

        OrshinSuugch::query()
            ->where(OrshinSuugch::ID, $member)
            ->where(OrshinSuugch::PARENT_ID, $orshinSuugch->id)
            ->delete();
    }
}
