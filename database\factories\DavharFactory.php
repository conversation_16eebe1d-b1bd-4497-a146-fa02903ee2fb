<?php

namespace Database\Factories;

use App\Models\Davhar;
use App\Models\Orc;
use Illuminate\Database\Eloquent\Factories\Factory;

class DavharFactory extends Factory
{
    protected $model = Davhar::class;

    public function definition(): array
    {
        return [
            'orc_id' => Orc::factory(),
            'number' => $this->faker->numberBetween(1, 20),
            'order' => $this->faker->numberBetween(1, 16),
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ];
    }
}
