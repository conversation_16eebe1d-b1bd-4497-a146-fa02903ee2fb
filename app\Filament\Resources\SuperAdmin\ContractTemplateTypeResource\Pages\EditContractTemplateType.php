<?php

namespace App\Filament\Resources\SuperAdmin\ContractTemplateTypeResource\Pages;

use App\Filament\Resources\SuperAdmin\ContractTemplateTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditContractTemplateType extends EditRecord
{
    protected static string $resource = ContractTemplateTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
