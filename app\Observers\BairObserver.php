<?php

namespace App\Observers;

use App\Models\Bair;
use App\Services\BairSyncService;
use Illuminate\Support\Facades\Log;

class BairObserver
{
    protected BairSyncService $bairSyncService;

    public function __construct(BairSyncService $bairSyncService)
    {
        $this->bairSyncService = $bairSyncService;
    }

    /**
     * Handle the Bair "created" event.
     *
     * @param Bair $bair
     * @return void
     */
    public function created(Bair $bair): void
    {
        Log::info('BairObserver: Bair created event triggered', [
            'bair_id' => $bair->id,
            'bair_name' => $bair->name
        ]);

        // Note: When a Bair is created, it typically doesn't have Korpus yet
        // The Korpus will be created separately and will trigger their own sync
        // So we don't need to do anything special here for CVSecurity
    }

    /**
     * Handle the Bair "updated" event.
     *
     * @param Bair $bair
     * @return void
     */
    public function updated(Bair $bair): void
    {
        Log::info('BairObserver: Bair updated event triggered', [
            'bair_id' => $bair->id,
            'bair_name' => $bair->name,
            'changed_fields' => array_keys($bair->getChanges())
        ]);

        // Sync with CVSecurity service to update related Korpus buildings
        $this->bairSyncService->syncUpdate($bair);
    }

    /**
     * Handle the Bair "deleted" event.
     *
     * @param Bair $bair
     * @return void
     */
    public function deleted(Bair $bair): void
    {
        Log::info('BairObserver: Bair deleted event triggered', [
            'bair_id' => $bair->id,
            'bair_name' => $bair->name
        ]);

        // Note: When a Bair is deleted, the related Korpus will also be deleted
        // due to foreign key constraints, and their observers will handle
        // the CVSecurity cleanup. So we don't need to do anything special here.
    }

    /**
     * Handle the Bair "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param Bair $bair
     * @return void
     */
    public function retrieved(Bair $bair): void
    {
        // Optionally sync read operations
        // Currently not needed for Bair
    }

    /**
     * Handle the Bair "restoring" event.
     *
     * @param Bair $bair
     * @return void
     */
    public function restoring(Bair $bair): void
    {
        Log::info('BairObserver: Bair restoring event triggered', [
            'bair_id' => $bair->id,
            'bair_name' => $bair->name
        ]);
    }

    /**
     * Handle the Bair "restored" event.
     *
     * @param Bair $bair
     * @return void
     */
    public function restored(Bair $bair): void
    {
        Log::info('BairObserver: Bair restored event triggered', [
            'bair_id' => $bair->id,
            'bair_name' => $bair->name
        ]);

        // When a Bair is restored, we should sync all its Korpus buildings
        // to ensure they are properly represented in CVSecurity
        $this->bairSyncService->syncAllKorpusBuildings($bair);
    }

    /**
     * Handle the Bair "force deleted" event.
     *
     * @param Bair $bair
     * @return void
     */
    public function forceDeleted(Bair $bair): void
    {
        Log::info('BairObserver: Bair force deleted event triggered', [
            'bair_id' => $bair->id,
            'bair_name' => $bair->name
        ]);

        // Note: Force deletion will cascade to Korpus, and their observers
        // will handle the CVSecurity cleanup
    }
}
