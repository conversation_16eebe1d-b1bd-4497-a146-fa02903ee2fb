<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Da<PERSON>har extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                => $this->id,
            'number'            => $this->number,
            'order'             => $this->order,
            'begin_toot_number' => $this->begin_toot_number,
            'end_toot_number'   => $this->end_toot_number,
            'code'              => $this->code,
            'orc'               => $this->orc ? new Orc($this->orc) : null,
        ];
    }
}
