<?php

namespace App\Providers;

use App\Models\Constant\ConstData;
use App\Models\Sukh;
use App\Models\Korpus;
use App\Models\Bair;
use App\Models\Orc;
use App\Models\Davhar;
use App\Models\OrshinSuugch;
use App\Observers\SukhObserver;
use App\Observers\KorpusObserver;
use App\Observers\BairObserver;
use App\Observers\OrcObserver;
use App\Observers\DavharObserver;
use App\Observers\OrshinSuugchObserver;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Str;
use Illuminate\Routing\Route;

use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
        $this->app->register(TelescopeServiceProvider::class);

        // Register CvSecurityService
        $this->app->singleton(\App\Services\CvSecurityService\CvSecurityService::class);

        // Register CvSecurityServiceExt
        $this->app->singleton(\App\Services\CvSecurityService\CvSecurityServiceExt::class);

        // Register SukhSyncService
        $this->app->singleton(\App\Services\SukhSyncService::class);

        // Register KorpusSyncService
        $this->app->singleton(\App\Services\KorpusSyncService::class);

        // Register BairSyncService
        $this->app->singleton(\App\Services\BairSyncService::class);

        // Register OrcSyncService
        $this->app->singleton(\App\Services\OrcSyncService::class);

        // Register DavharSyncService
        $this->app->singleton(\App\Services\DavharSyncService::class);

        // Register OrshinSuugchSyncService with explicit dependency injection
        $this->app->singleton(\App\Services\OrshinSuugchSyncService::class, function ($app) {
            return new \App\Services\OrshinSuugchSyncService(
                $app->make(\App\Services\CvSecurityService\CvSecurityService::class),
                $app->make(\App\Services\CvSecurityService\CvSecurityServiceExt::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (!$this->app->environment(ConstData::LOCAL)) {
            URL::forceScheme('https');
        }

        // Register model observers
        Sukh::observe(SukhObserver::class);
        Korpus::observe(KorpusObserver::class);
        Bair::observe(BairObserver::class);
        Orc::observe(OrcObserver::class);
        Davhar::observe(DavharObserver::class);
        OrshinSuugch::observe(OrshinSuugchObserver::class);

        Scramble::routes(function (Route $route) {
            return Str::startsWith($route->uri, 'api/');
        });

        Scramble::afterOpenApiGenerated(function (OpenApi $openApi) {
            $openApi->secure(
                SecurityScheme::http('bearer')
            );
        });
    }
}
