{"swagger": "2.0", "info": {"title": "ZKBio CVSecurity API Elevator", "version": "1.0.0", "description": "API for ZKBio CVSecurity system"}, "host": "*************:8098", "basePath": "/", "tags": [{"name": "EleDevice", "description": "ele device"}, {"name": "EleFloor", "description": "ele floor"}, {"name": "EleLevel", "description": "ele level"}, {"name": "EleTransaction", "description": "ele transaction"}, {"name": "PersBioTemplate", "description": "person bioTemplate"}, {"name": "PersCard", "description": "person card"}, {"name": "PersDepartment ", "description": "person department"}, {"name": "Person", "description": "person"}], "paths": {"/api/bioTemplate/add": {"post": {"tags": ["PersBioTemplate"], "summary": "Add BioTemplate", "description": "Add BioTemplate", "operationId": "addUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "apiBioTemplate", "description": "apiBioTemplate", "required": true, "schema": {"$ref": "#/definitions/PersApiBioTemplateItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/bioTemplate/delete/{pin}{templateNo}": {"post": {"tags": ["PersBioTemplate"], "summary": "Delete BioTemplate By pin And templateNo", "description": "Delete BioTemplate By pin And templateNo", "operationId": "deleteUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}, {"name": "templateNo", "in": "query", "description": "templateNo", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/bioTemplate/deleteByPin/{pin}": {"post": {"tags": ["PersBioTemplate"], "summary": "deleteByPin BioTemplate", "description": "deleteById BioTemplate", "operationId": "deleteAllUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/bioTemplate/getFgListByPin/{pin}": {"get": {"tags": ["PersBioTemplate"], "summary": "Get BioTemplate List", "description": "Get BioTemplate List", "operationId": "listUsingGET", "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/card/getCards/{pin}": {"get": {"tags": ["PersCard"], "summary": "Get Card List By Pin", "description": "Return Card List", "operationId": "getCardsByPinUsingGET", "produces": ["application/json"], "parameters": [{"name": "pin", "in": "path", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/card/set": {"post": {"tags": ["PersCard"], "summary": "Set Card To Person", "description": "Return Result Object", "operationId": "setCardToPersonUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "card", "description": "card", "required": true, "schema": {"$ref": "#/definitions/PersApiCardItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/department/add": {"post": {"tags": ["PersDepartment "], "summary": "Add Department", "description": "Create Or Update Department", "operationId": "addUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "department", "description": "department", "required": true, "schema": {"$ref": "#/definitions/PersApiDepartmentItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/department/delete/{code}": {"post": {"tags": ["PersDepartment "], "summary": "Delete Department", "description": "Delete Department By Code", "operationId": "deleteByCodeUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "code", "in": "path", "description": "code", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/department/get/{code}": {"get": {"tags": ["PersDepartment "], "summary": "Get Department", "description": "Get Department By Code", "operationId": "getByCodeUsingGET", "produces": ["application/json"], "parameters": [{"name": "code", "in": "path", "description": "code", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/department/getDepartmentList": {"post": {"tags": ["PersDepartment "], "summary": "Get Department List By DeptCode", "description": "Return Departments List", "operationId": "getDepartmentListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleDevice/eleList": {"get": {"tags": ["EleDevice"], "summary": "Get Ele Devices", "description": "Return Ele Devices List", "operationId": "eleListUsingGET", "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleDevice/getEle": {"get": {"tags": ["EleDevice"], "summary": "Get Ele Devices By Sn", "description": "Return Ele <PERSON>ce List", "operationId": "getEleListBySnUsingGET", "produces": ["application/json"], "parameters": [{"name": "sn", "in": "query", "description": "sn", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/addPersonLevelByLevel": {"post": {"tags": ["EleLevel"], "summary": "Add Person Level By Level", "description": "Return Result Object", "operationId": "addPersonLevelByLevelUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "levelId", "in": "query", "description": "levelId", "required": true, "type": "string"}, {"name": "pins", "in": "query", "description": "pins", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/addPersonLevelByPerson": {"post": {"tags": ["EleLevel"], "summary": "Add Person Level By Person", "description": "Return Result Object", "operationId": "addPersonLevelByPersonUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "levelIds", "in": "query", "description": "levelIds", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/deleteLevel": {"post": {"tags": ["EleLevel"], "summary": "Del Ele Level", "description": "Return Result Object", "operationId": "deleteLevelUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "levelIds", "in": "query", "description": "levelIds", "required": true, "type": "string"}, {"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/getById": {"get": {"tags": ["EleLevel"], "summary": "Get Ele Level By LevelId", "description": "Return Ele Level", "operationId": "getEleLevelByLevelIdUsingGET", "produces": ["application/json"], "parameters": [{"name": "id", "in": "query", "description": "id", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/getByName": {"get": {"tags": ["EleLevel"], "summary": "Get Ele Level By LevelName", "description": "Return Ele Level", "operationId": "getEleLevelByLevelNameUsingGET", "produces": ["application/json"], "parameters": [{"name": "name", "in": "query", "description": "name", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/list": {"get": {"tags": ["EleLevel"], "summary": "Get Ele Level List", "description": "Return Ele Level List", "operationId": "getEleLevelListUsingGET", "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/syncLevel": {"post": {"tags": ["EleLevel"], "summary": "Sync Ele Level To Dev", "description": "Return Result Object", "operationId": "syncEleLevelToDevUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "levelId", "in": "query", "description": "levelId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleLevel/syncPerson": {"post": {"tags": ["EleLevel"], "summary": "Sync Ele Person Level", "description": "Return Result Object", "operationId": "syncElePersonToDevUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "levelIds", "in": "query", "description": "levelIds", "required": true, "type": "string"}, {"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/eleTransaction/list": {"get": {"tags": ["EleTransaction"], "summary": "Get Ele Transactions List", "description": "Return Ele Transactions List", "operationId": "listEleTransactionUsingGET", "produces": ["application/json"], "parameters": [{"name": "endDate", "in": "query", "description": "endDate", "required": false, "type": "string"}, {"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}, {"name": "personPin", "in": "query", "description": "personPin", "required": false, "type": "string"}, {"name": "startDate", "in": "query", "description": "startDate", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/floor/list": {"get": {"tags": ["EleFloor"], "summary": "Get Ele Floors", "description": "Return Ele Floor List", "operationId": "floorListUsingGET", "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/floor/remoteCloseById": {"post": {"tags": ["EleFloor"], "summary": "Close Ele Floor By Id", "description": "Return Result Object", "operationId": "lockingEleFloorUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "floorId", "in": "query", "description": "floorId", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/floor/remoteOpenById": {"post": {"tags": ["EleFloor"], "summary": "Open Ele Floor By Id", "description": "Return Result Object", "operationId": "openEleFloorUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "floorId", "in": "query", "description": "floorId", "required": true, "type": "string"}, {"name": "interval", "in": "query", "description": "interval", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/add": {"post": {"tags": ["Person"], "summary": "Add Person", "description": "Create Or Update Person", "operationId": "addPersonUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "person", "description": "person", "required": true, "schema": {"$ref": "#/definitions/PersApiPersonItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/addPersonnelBasicInfo": {"post": {"tags": ["Person"], "summary": "Add Personnel Basic Information", "description": "Create Or Update Person", "operationId": "addPersonnelBasicInfoUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "person", "description": "person", "required": true, "schema": {"$ref": "#/definitions/ApiPersonBaseInfoItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/delete/{pin}": {"post": {"tags": ["Person"], "summary": "Delete Person", "description": "Delete Person By Pin", "operationId": "deleteByPinUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "path", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/get/{pin}": {"get": {"tags": ["Person"], "summary": "Get Person", "description": "Get Person By Pin", "operationId": "getByPinUsingGET", "produces": ["application/json"], "parameters": [{"name": "pin", "in": "path", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/getPersonList": {"post": {"tags": ["Person"], "summary": "Get Person List By PinList And DeptCode", "description": "Return Persons List", "operationId": "getPersonListUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "deptCodes", "in": "query", "description": "deptCodes", "required": false, "type": "string"}, {"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}, {"name": "pins", "in": "query", "description": "pins", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/getQrCode/{pin}": {"post": {"tags": ["Person"], "summary": "Get Dynamic QR code", "description": "Get Dynamic QR code By Pin", "operationId": "getQrCodeByPinUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "path", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/leave": {"post": {"tags": ["Person"], "summary": "Leave Person", "description": "Leave Person", "operationId": "leaveUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "persApiLeavePersonItem", "description": "persApiLeavePersonItem", "required": true, "schema": {"$ref": "#/definitions/PersApiLeavePersonItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/reinstated": {"post": {"tags": ["Person"], "summary": "Reinstated", "description": "Reinstated Person", "operationId": "reinstatedPersonUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "person", "description": "person", "required": true, "schema": {"$ref": "#/definitions/PersApiPersonItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/person/updatePersonnelPhoto": {"post": {"tags": ["Person"], "summary": "Update Personnel Photo", "description": "Update Personnel Photo", "operationId": "updatePersonnelPhotoUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "person", "description": "person", "required": true, "schema": {"$ref": "#/definitions/PersApiPhotoItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/bioTemplate/delete": {"post": {"tags": ["PersBioTemplate"], "summary": "Delete BioTemplate By pin And templateNo", "description": "Delete BioTemplate By pin And templateNo", "operationId": "deleteUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}, {"name": "templateNo", "in": "query", "description": "templateNo", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/bioTemplate/deleteByPin": {"post": {"tags": ["PersBioTemplate"], "summary": "deleteByPin BioTemplate", "description": "deleteById BioTemplate", "operationId": "deleteAllUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/bioTemplate/getFgListByPin": {"get": {"tags": ["PersBioTemplate"], "summary": "Get BioTemplate List", "description": "Get BioTemplate List", "operationId": "listUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/card/getCards": {"get": {"tags": ["PersCard"], "summary": "Get Card List By Pin", "description": "Return Card List", "operationId": "getCardsByPinUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/department/delete": {"post": {"tags": ["PersDepartment "], "summary": "Delete Department", "description": "Delete Department By Code", "operationId": "deleteByCodeUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/department/get": {"get": {"tags": ["PersDepartment "], "summary": "Get Department", "description": "Get Department By Code", "operationId": "getByCodeUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "code", "in": "query", "description": "code", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/eleDevice/eleList": {"get": {"tags": ["EleDevice"], "summary": "Get Ele Devices", "description": "Return Ele Devices List", "operationId": "eleListUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/eleLevel/list": {"get": {"tags": ["EleLevel"], "summary": "Get Ele Level List", "description": "Return Ele Level List", "operationId": "getEleLevelListUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/eleTransaction/list": {"get": {"tags": ["EleTransaction"], "summary": "Get Ele Transactions List", "description": "Return Ele Transactions List", "operationId": "listEleTransactionUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "endDate", "in": "query", "description": "endDate", "required": false, "type": "string"}, {"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}, {"name": "personPin", "in": "query", "description": "personPin", "required": false, "type": "string"}, {"name": "startDate", "in": "query", "description": "startDate", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/floor/list": {"get": {"tags": ["EleFloor"], "summary": "Get Ele Floors", "description": "Return Ele Floor List", "operationId": "floorListUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/addPersons": {"post": {"tags": ["Person"], "summary": "<PERSON><PERSON> Add Person", "description": "Batch Create Or Update Person", "operationId": "addPersonsUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "persApiPersonItemList", "description": "persApiPersonItemList", "required": true, "schema": {"type": "array", "items": {"$ref": "#/definitions/PersApiPersonItem"}}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/delete": {"post": {"tags": ["Person"], "summary": "Delete Person", "description": "Delete Person By Pin", "operationId": "deleteByPinUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/deleteByPins": {"post": {"tags": ["Person"], "summary": "<PERSON><PERSON> Delete Person", "description": "Delete Person By Pins", "operationId": "deleteByPinsUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pins", "in": "query", "description": "pins", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/detectFace": {"post": {"tags": ["Person"], "summary": "Detecting faces", "description": "Detecting faces", "operationId": "detectFaceUsingPOST", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "body", "name": "persApiFacePhotoItem", "description": "persApiFacePhotoItem", "required": true, "schema": {"$ref": "#/definitions/PersApiFacePhotoItem"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/get": {"get": {"tags": ["Person"], "summary": "Get Person", "description": "Get Person By Pin", "operationId": "getByPinUsingGET_1", "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/getPersonList": {"post": {"tags": ["Person"], "summary": "Get Person List By PinList And DeptCode", "description": "Return Persons List", "operationId": "getPersonListUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "deptCodes", "in": "query", "description": "deptCodes", "required": false, "type": "string"}, {"name": "pageNo", "in": "query", "description": "pageNo", "required": true, "type": "integer", "format": "int32"}, {"name": "pageSize", "in": "query", "description": "pageSize", "required": true, "type": "integer", "format": "int32"}, {"name": "pins", "in": "query", "description": "pins", "required": false, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}, "/api/v2/person/getQrCode": {"post": {"tags": ["Person"], "summary": "Get Dynamic QR code", "description": "Get Dynamic QR code By Pin", "operationId": "getQrCodeByPinUsingPOST_1", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"name": "pin", "in": "query", "description": "pin", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ApiResultMessage"}}, "201": {"description": "Created"}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found"}}}}}, "definitions": {"ApiPersonBaseInfoItem": {"type": "object", "properties": {"birthday": {"type": "string", "description": "YYYY-MM-DD", "enum": ["2016-07-15"]}, "cardNo": {"type": "string", "description": "main card", "enum": ["123456789"]}, "certNumber": {"type": "string", "enum": ["12345678"]}, "certType": {"type": "string", "enum": ["2"]}, "deptCode": {"type": "string", "enum": ["1"]}, "email": {"type": "string", "enum": ["<EMAIL>"]}, "gender": {"type": "string", "description": "F:Female M:Male", "enum": ["F"]}, "hireDate": {"type": "string", "format": "date-time", "example": "2019-06-10", "description": "入职时间"}, "isSendMail": {"type": "boolean"}, "lastName": {"type": "string", "enum": ["lastName"]}, "mobilePhone": {"type": "string", "enum": ["15123456789"]}, "name": {"type": "string", "enum": ["max"]}, "personPwd": {"type": "string", "enum": ["123456"]}, "pin": {"type": "string", "enum": ["1234567"]}, "ssn": {"type": "string", "enum": ["111111"]}, "supplyCards": {"type": "string", "description": "card1,card2", "enum": ["987643"]}}, "title": "ApiPersonBaseInfoItem"}, "ApiResultMessage": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "message": {"type": "string"}}, "title": "ApiResultMessage"}, "PersApiBioTemplateItem": {"type": "object", "properties": {"pin": {"type": "string", "enum": ["1"]}, "template": {"type": "string", "enum": ["fsfsfs"]}, "templateNo": {"type": "string", "enum": ["3"]}, "validType": {"type": "string", "enum": ["1 or 3"]}, "version": {"type": "string", "enum": ["10"]}}, "title": "PersApiBioTemplateItem"}, "PersApiCardItem": {"type": "object", "properties": {"cardNo": {"type": "string", "enum": ["1234567"]}, "cardType": {"type": "string", "enum": ["0 or 1"]}, "pin": {"type": "string", "enum": ["123"]}}, "title": "PersApiCardItem"}, "PersApiDepartmentItem": {"type": "object", "properties": {"code": {"type": "string"}, "name": {"type": "string"}, "parentCode": {"type": "string"}, "sortNo": {"type": "integer", "format": "int32", "enum": [999999]}}, "title": "PersApiDepartmentItem"}, "PersApiFacePhotoItem": {"type": "object", "properties": {"personPhoto": {"type": "string", "enum": ["Base64"]}}, "title": "PersApiFacePhotoItem"}, "PersApiLeavePersonItem": {"type": "object", "properties": {"leaveDate": {"type": "string", "format": "date-time", "example": "2019-06-10", "description": "离职日期"}, "leaveType": {"type": "string", "enum": ["1"]}, "pin": {"type": "string", "enum": ["1234567"]}}, "title": "PersApiLeavePersonItem"}, "PersApiPersonItem": {"type": "object", "properties": {"accEndTime": {"type": "string", "enum": ["2019-07-14 08:56:00"]}, "accLevelIds": {"type": "string", "description": "L1,L2", "enum": ["1"]}, "accStartTime": {"type": "string", "enum": ["2018-07-14 08:56:00"]}, "birthday": {"type": "string", "description": "YYYY-MM-DD", "enum": ["2016-07-15"]}, "carPlate": {"type": "string", "enum": ["闽_A12345"]}, "cardNo": {"type": "string", "description": "main card", "enum": ["123456789"]}, "certNumber": {"type": "string", "enum": ["123456"]}, "certType": {"type": "string", "enum": ["2"]}, "deptCode": {"type": "string", "enum": ["1"]}, "email": {"type": "string", "enum": ["<EMAIL>"]}, "gender": {"type": "string", "description": "F:Female M:Male", "enum": ["F"]}, "hireDate": {"type": "string", "format": "date-time", "example": "2019-06-10", "description": "入职时间"}, "isDisabled": {"type": "object", "example": false, "description": "禁止名单"}, "isSendMail": {"type": "boolean"}, "lastName": {"type": "string", "enum": ["lastName"]}, "leaveId": {"type": "string"}, "mobilePhone": {"type": "string", "enum": ["15123456789"]}, "name": {"type": "string", "enum": ["max"]}, "personPhoto": {"type": "string", "enum": ["Base64"]}, "personPwd": {"type": "string", "enum": ["123456"]}, "pin": {"type": "string", "enum": ["1234567"]}, "ssn": {"type": "string", "enum": ["111111"]}, "supplyCards": {"type": "string", "description": "card1,card2", "enum": ["987643"]}}, "title": "PersApiPersonItem"}, "PersApiPhotoItem": {"type": "object", "properties": {"personPhoto": {"type": "string", "enum": ["Base64"]}, "pin": {"type": "string", "enum": ["1234567"]}}, "title": "PersApiPhotoItem"}, "ResultMessage": {"type": "object", "properties": {"attributes": {"type": "object"}, "msg": {"type": "string"}, "obj": {"type": "object"}, "success": {"type": "boolean"}}, "title": "ResultMessage"}}}