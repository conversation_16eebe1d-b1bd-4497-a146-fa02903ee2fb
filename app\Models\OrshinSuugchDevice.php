<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\OrshinSuugchDevice
 *
 * @mixin IdeHelperOrshinSuugchDevice
 * @property-read \App\Models\OrshinSuugch|null $orshin_suugch
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchDevice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchDevice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchDevice query()
 * @mixin \Eloquent
 */
class OrshinSuugchDevice extends Model
{
    use HasFactory;

    const ID                          = 'id';
    const ORSHIN_SUUGCH_ID            = 'orshin_suugch_id';
    const MAC_ADDRESS                 = 'mac_address';

    const RELATION_ORSHIN_SUUGCH      = 'orshin_suugch';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::ORSHIN_SUUGCH_ID,
        self::MAC_ADDRESS,
    ];

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }
}
