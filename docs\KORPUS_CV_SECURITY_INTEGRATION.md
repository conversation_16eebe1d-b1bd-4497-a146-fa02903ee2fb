# Korpus CVSecurity Integration

This document describes the integration between the Korpus model and the CVSecurity service, which automatically synchronizes building data between the application and the external CVSecurity system.

**Note**: This integration also includes Bair synchronization - when a Bair name is updated, all related Korpus buildings in CVSecurity are automatically updated with the new building names.

## Overview

The integration provides automatic synchronization of Korpus (building) data with the CVSecurity service through:

-   **Automatic CRUD synchronization**: Create, update, and delete operations are automatically synced
-   **Building name construction**: Building names are constructed as "{building_name} {korpus_name}"
-   **Parent Sukh association**: Buildings are linked to their parent Sukh (residents' committee)
-   **Error handling**: Graceful handling of service unavailability
-   **Logging**: Comprehensive logging of all sync operations

## Architecture

The integration consists of several components:

1. **KorpusObserver** (`app/Observers/KorpusObserver.php`)

    - Listens to Korpus model events (created, updated, deleted)
    - Triggers appropriate CVSecurity sync operations

2. **BairObserver** (`app/Observers/BairObserver.php`)

    - Listens to Bair model events (updated)
    - Triggers updates for all related Korpus buildings when Bair name changes

3. **KorpusSyncService** (`app/Services/KorpusSyncService.php`)

    - Handles the actual synchronization with CVSecurity
    - Manages error handling and logging
    - Extracts codes from CVSecurity responses
    - Constructs building names and parent relationships

4. **BairSyncService** (`app/Services/BairSyncService.php`)

    - Handles Bair-related synchronization with CVSecurity
    - Updates all related Korpus buildings when Bair name changes
    - Provides manual sync functionality for all Korpus in a Bair

5. **CvSecurityServiceExt** (`app/Services/CvSecurityService/CvSecurityServiceExt.php`)

    - Extended with building-related methods
    - Provides CRUD operations for Buildings in CVSecurity

6. **Database Migration** (`database/migrations/2025_05_27_062043_add_code_to_korpuses_table.php`)
    - Adds the `code` field to the korpuses table

## Database Schema Changes

### New Field: `code`

-   **Type**: `string`, nullable
-   **Purpose**: Stores the CVSecurity response code
-   **Location**: Added after `end_toot_number` field in korpuses table

```sql
ALTER TABLE korpuses ADD COLUMN code VARCHAR(255) NULL AFTER end_toot_number;
```

## Configuration

### Environment Variables

```env
CV_SECURITY_HOST=127.0.0.1
CV_SECURITY_PORT=8001
CV_SECURITY_API_KEY=1234567890abcdef
```

### Service Registration

The services are registered in `app/Providers/AppServiceProvider.php`:

```php
// Register CvSecurityServiceExt
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityServiceExt::class);

// Register KorpusSyncService
$this->app->singleton(\App\Services\KorpusSyncService::class);

// Register BairSyncService
$this->app->singleton(\App\Services\BairSyncService::class);
```

### Observer Registration

The observer is registered in `app/Providers/AppServiceProvider.php`:

```php
// Register model observers
Korpus::observe(KorpusObserver::class);
Bair::observe(BairObserver::class);
```

## Usage

### Automatic Synchronization

The integration works automatically. When you perform any CRUD operation on a Korpus:

```php
// Create - automatically syncs with CVSecurity
$korpus = Korpus::create([
    'bair_id' => 1,
    'name' => 'Test Korpus',
    'order' => 1,
    'begin_toot_number' => 1,
    'end_toot_number' => 100,
]);

// The code field will be automatically populated
echo $korpus->code; // e.g., "BLDG_00001"

// Update - automatically syncs with CVSecurity
$korpus->update(['name' => 'Updated Korpus']);

// Delete - automatically syncs with CVSecurity
$korpus->delete();
```

### Building Name Construction

The building name sent to CVSecurity is constructed with conditional separator logic:

```
{bair_name}{separator}{korpus_name}
```

Where separator is:

-   `-` (dash) if korpus name starts with a digit
-   `` (empty string) if korpus name starts with a letter

Examples:

-   Bair name: "Building 57", Korpus name: "1st Block" → CVSecurity name: "Building 57-1st Block"
-   Bair name: "Building 57", Korpus name: "Block A" → CVSecurity name: "Building 57Block A"
-   Bair name: "Central", Korpus name: "2nd Floor" → CVSecurity name: "Central-2nd Floor"
-   Bair name: "Central", Korpus name: "Main Wing" → CVSecurity name: "CentralMain Wing"

### Parent Sukh Association

Buildings require a parent Sukh in CVSecurity. The integration automatically:

1. Loads the Korpus -> Bair -> Sukh relationship
2. Uses the Sukh's CVSecurity code as the parent_code
3. Includes this in the building creation/update data

### Bair Name Change Synchronization

When a Bair name is updated, all related Korpus buildings in CVSecurity are automatically updated:

```php
// Update Bair name - automatically updates all related Korpus buildings
$bair = Bair::find(1);
$bair->update(['name' => 'New Building Name']);

// All Korpus buildings belonging to this Bair will be updated in CVSecurity
// with new building names: "New Building Name{separator}{korpus_name}"
```

**Example:**

-   Original Bair name: "Building A"
-   Korpus names: "1st Floor", "Block B"
-   Original CVSecurity names: "Building A-1st Floor", "Building ABlock B"
-   After Bair update to "Central Tower":
-   New CVSecurity names: "Central Tower-1st Floor", "Central TowerBlock B"

### Manual Testing

Use the provided test command:

```bash
# Check CVSecurity service status
php artisan test:korpus-cv-security status

# Test creation
php artisan test:korpus-cv-security create

# Test update
php artisan test:korpus-cv-security update

# Test deletion
php artisan test:korpus-cv-security delete

# Test naming logic
php artisan test:korpus-cv-security naming

# Test Bair integration (updates related Korpus buildings)
php artisan test:bair-cv-security status
php artisan test:bair-cv-security update
php artisan test:bair-cv-security sync-all
```

## Error Handling

### Service Unavailable

When CVSecurity service is unavailable:

-   The `code` field is set to `null`
-   A warning is logged
-   The Korpus operation continues normally

### Missing Parent Sukh

When a Korpus doesn't have a valid parent Sukh:

-   The operation fails with an error
-   The issue is logged for investigation
-   The `code` field remains `null`

### API Errors

When CVSecurity API returns errors:

-   The error response is logged
-   The `code` field is set to `null`
-   The local operation continues

## Logging

All sync operations are logged with appropriate levels:

-   **INFO**: Successful operations
-   **WARNING**: Service unavailable
-   **ERROR**: API failures, exceptions

Example log entries:

```
[2025-05-27 08:06:05] local.INFO: KorpusObserver: Korpus created event triggered {"korpus_id":2,"korpus_name":"Test Korpus"}
[2025-05-27 08:06:05] local.INFO: Korpus successfully synced with CVSecurity on creation {"korpus_id":2,"cv_code":"BLDG_00001"}
```

## Troubleshooting

### Common Issues

1. **Code field not populated**

    - Check CVSecurity service status: `php artisan test:korpus-cv-security status`
    - Verify environment variables are set correctly
    - Check logs for error messages

2. **Observer not triggering**

    - Ensure observer is registered in AppServiceProvider
    - Clear application cache: `php artisan cache:clear`
    - Restart application server

3. **Parent Sukh errors**

    - Verify the Bair has a valid sukh_id
    - Ensure the parent Sukh has a CVSecurity code
    - Check the Korpus -> Bair -> Sukh relationship

4. **API authentication errors**
    - Verify `CV_SECURITY_API_KEY` is correct
    - Check CVSecurity service logs

### Debug Commands

```bash
# Check service status
php artisan test:korpus-cv-security status

# View recent logs
Get-Content storage/logs/laravel.log -Tail 50

# Clear cache
php artisan cache:clear
php artisan config:clear
```

## Best Practices

1. **Always ensure parent Sukh exists** before creating Korpus
2. **Monitor logs** for sync failures
3. **Handle null codes gracefully** in your application logic
4. **Test thoroughly** after any CVSecurity service updates
5. **Keep API keys secure** and rotate them regularly

## API Endpoints Used

The integration uses the following CVSecurity API endpoints:

-   `POST /buildings/` - Create building
-   `GET /buildings/{code}` - Get building by code
-   `PUT /buildings/{code}` - Update building
-   `DELETE /buildings/{code}` - Delete building
-   `GET /buildings/next-code` - Get next available code

## Data Structure

### Request Data

```json
{
    "name": "Central Building Block A",
    "parent_code": "SUKH_0001"
}
```

### Response Data

```json
{
    "code": "BLDG_00001",
    "name": "Central Building Block A",
    "parent_code": "SUKH_0001",
    "created_at": "2025-05-27T08:06:05Z"
}
```
