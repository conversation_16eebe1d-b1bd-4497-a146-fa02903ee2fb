<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreOrcTagRequest extends FormRequest
{
    const PARAMETER_ORC_ID = 'orc_id';
    const PARAMETER_CODE   = 'tag_code';

    public function rules(): array
    {
        return [
            'orc_id'   => ['required', 'exists:orcs,id'],
            'tag_code' => ['required', 'string', 'max:255', 'unique:orc_tags,tag_code'],
        ];
    }
}
