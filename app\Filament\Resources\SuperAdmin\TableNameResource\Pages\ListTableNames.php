<?php

namespace App\Filament\Resources\SuperAdmin\TableNameResource\Pages;

use App\Filament\Resources\SuperAdmin\TableNameResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListTableNames extends ListRecords
{
    protected static string $resource = TableNameResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
