<?php

namespace App\Console\Commands;

use App\Models\Korpus;
use App\Models\Bair;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use App\Services\KorpusSyncService;
use Illuminate\Console\Command;

class TestKorpusCvSecurityIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:korpus-cv-security {action=status : Action to perform (status|create|update|delete|naming)}';

    /**
     * The console description of the command.
     *
     * @var string
     */
    protected $description = 'Test Korpus CVSecurity integration';

    protected CvSecurityServiceExt $cvSecurityService;
    protected KorpusSyncService $korpusSyncService;

    public function __construct(CvSecurityServiceExt $cvSecurityService, KorpusSyncService $korpusSyncService)
    {
        parent::__construct();
        $this->cvSecurityService = $cvSecurityService;
        $this->korpusSyncService = $korpusSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->checkServiceStatus();
                break;
            case 'create':
                $this->testCreateKorpus();
                break;
            case 'update':
                $this->testUpdateKorpus();
                break;
            case 'delete':
                $this->testDeleteKorpus();
                break;
            case 'naming':
                $this->testNamingLogic();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info("Available actions: status, create, update, delete, naming");
                return 1;
        }

        return 0;
    }

    protected function checkServiceStatus()
    {
        $this->info('Checking CVSecurity service status...');

        $isAvailable = $this->cvSecurityService->isServiceAvailable();

        if ($isAvailable) {
            $this->info('✅ CVSecurity service is available');
        } else {
            $this->warn('⚠️ CVSecurity service is not available');
            $this->info('Please check:');
            $this->info('- Service is running');
            $this->info('- Environment variables are set correctly');
            $this->info('- Network connectivity');
        }

        // Show configuration
        $this->info("\nConfiguration:");
        $this->table(
            ['Setting', 'Value'],
            [
                ['Host', config('services.cv_security_ext.host', 'Not set')],
                ['Port', config('services.cv_security_ext.port', 'Not set')],
                ['API Key', config('services.cv_security_ext.api_key') ? 'Set' : 'Not set'],
            ]
        );
    }

    protected function testCreateKorpus()
    {
        $this->info('Testing Korpus creation with CVSecurity integration...');

        // Find or create a test Bair
        $bair = Bair::first();
        if (!$bair) {
            $this->error('No Bair found in database. Please create a Bair first.');
            return;
        }

        // Create test Korpus with different naming patterns
        $testNames = [
            '1st Block',           // Starts with digit - should use '-'
            'Block A',             // Starts with letter - should use ''
            '2nd Floor',           // Starts with digit - should use '-'
            'Main Building',       // Starts with letter - should use ''
        ];

        $testName = $testNames[array_rand($testNames)];

        // Create a test Korpus
        $korpus = Korpus::create([
            'bair_id' => $bair->id,
            'name' => $testName,
            'order' => rand(1000, 9999),
            'begin_toot_number' => 1,
            'end_toot_number' => 100,
        ]);

        $this->info("Created Korpus with ID: {$korpus->id}");

        // Wait a moment for the observer to process
        sleep(2);

        // Refresh the model to get the updated code
        $korpus->refresh();

        if ($korpus->code) {
            $this->info("✅ CVSecurity code assigned: {$korpus->code}");
        } else {
            $this->warn("⚠️ No CVSecurity code assigned (service might be unavailable)");
        }

        $this->table(
            ['Field', 'Value'],
            [
                ['ID', $korpus->id],
                ['Name', $korpus->name],
                ['Bair', $korpus->bair->name ?? 'N/A'],
                ['Building Name (CVSecurity)', ($korpus->bair->name ?? 'Unknown Building') . ' ' . $korpus->name],
                ['Order', $korpus->order],
                ['Begin Toot Number', $korpus->begin_toot_number],
                ['End Toot Number', $korpus->end_toot_number],
                ['CV Security Code', $korpus->code ?: 'Not assigned'],
            ]
        );
    }

    protected function testUpdateKorpus()
    {
        $this->info('Testing Korpus update with CVSecurity integration...');

        // Find the latest test Korpus
        $korpus = Korpus::where('name', 'like', 'Test Korpus%')->latest()->first();

        if (!$korpus) {
            $this->error('No test Korpus found. Run "create" action first.');
            return;
        }

        $this->info("Updating Korpus ID: {$korpus->id}");

        // Update the Korpus
        $korpus->update([
            'name' => $korpus->name . ' (Updated)',
            'begin_toot_number' => rand(1, 50),
            'end_toot_number' => rand(51, 200),
        ]);

        $this->info("✅ Korpus updated successfully");

        // Wait a moment for the observer to process
        sleep(2);

        // Refresh the model
        $korpus->refresh();

        $this->table(
            ['Field', 'Value'],
            [
                ['ID', $korpus->id],
                ['Name', $korpus->name],
                ['Bair', $korpus->bair->name ?? 'N/A'],
                ['Building Name (CVSecurity)', ($korpus->bair->name ?? 'Unknown Building') . ' ' . $korpus->name],
                ['Begin Toot Number', $korpus->begin_toot_number],
                ['End Toot Number', $korpus->end_toot_number],
                ['CV Security Code', $korpus->code ?: 'Not assigned'],
            ]
        );
    }

    protected function testDeleteKorpus()
    {
        $this->info('Testing Korpus deletion with CVSecurity integration...');

        // Find the latest test Korpus
        $korpus = Korpus::where('name', 'like', 'Test Korpus%')->latest()->first();

        if (!$korpus) {
            $this->error('No test Korpus found. Run "create" action first.');
            return;
        }

        $korpusId = $korpus->id;
        $korpusCode = $korpus->code;
        $buildingName = ($korpus->bair->name ?? 'Unknown Building') . ' ' . $korpus->name;

        $this->info("Deleting Korpus ID: {$korpusId} with CV Code: " . ($korpusCode ?: 'None'));
        $this->info("Building Name: {$buildingName}");

        // Delete the Korpus
        $korpus->delete();

        $this->info("✅ Korpus deleted successfully");
        $this->info("CVSecurity deletion sync should have been triggered automatically");
    }

    protected function testNamingLogic()
    {
        $this->info('Testing building name construction logic...');

        // Find or create a test Bair
        $bair = Bair::first();
        if (!$bair) {
            $this->error('No Bair found in database. Please create a Bair first.');
            return;
        }

        $testCases = [
            '1st Block',           // Starts with digit - should use '-'
            'Block A',             // Starts with letter - should use ''
            '2nd Floor',           // Starts with digit - should use '-'
            'Main Building',       // Starts with letter - should use ''
            '3rd Wing',            // Starts with digit - should use '-'
            'Tower B',             // Starts with letter - should use ''
        ];

        $results = [];

        foreach ($testCases as $testName) {
            $buildingName = $bair->name;
            $separator = is_numeric(substr($testName, 0, 1)) ? '-' : '';
            $fullBuildingName = $buildingName . $separator . $testName;

            $results[] = [
                'Korpus Name' => $testName,
                'Starts with Digit' => is_numeric(substr($testName, 0, 1)) ? 'Yes' : 'No',
                'Separator' => $separator ?: '(none)',
                'Full Building Name' => $fullBuildingName,
            ];
        }

        $this->table(
            ['Korpus Name', 'Starts with Digit', 'Separator', 'Full Building Name'],
            $results
        );

        $this->info('✅ Naming logic test completed');
        $this->info('Building names are constructed as: {bair_name}{separator}{korpus_name}');
        $this->info('Where separator is "-" if korpus name starts with digit, otherwise empty string');
    }
}
