<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Services\CvSecurityService\CvSecurityService;
use App\Services\CvSecurityService\CvSecurityServiceExt;

class CvSecurityServiceMigrationTest extends TestCase
{
    /**
     * Test that both CV Security services can be instantiated
     */
    public function test_both_cv_security_services_can_be_instantiated()
    {
        // Test new service (using cv_security config)
        $newService = app(CvSecurityService::class);
        $this->assertInstanceOf(CvSecurityService::class, $newService);

        // Test existing service (now using cv_security_ext config)
        $existingService = app(CvSecurityServiceExt::class);
        $this->assertInstanceOf(CvSecurityServiceExt::class, $existingService);
    }

    /**
     * Test that services use different configurations
     */
    public function test_services_use_different_configurations()
    {
        // Set test configuration
        config([
            'services.cv_security.host' => 'new-host',
            'services.cv_security.port' => '8098',
            'services.cv_security_ext.host' => 'ext-host',
            'services.cv_security_ext.port' => '8078',
        ]);

        $newService = app(CvSecurityService::class);
        $existingService = app(CvSecurityServiceExt::class);

        $newStatus = $newService->getServiceStatus();
        $existingStatus = $existingService->getServiceStatus();

        // Verify they use different hosts
        $this->assertEquals('new-host', $newStatus['host']);
        $this->assertEquals('8098', $newStatus['port']);

        $this->assertEquals('ext-host', $existingStatus['host']);
        $this->assertEquals('8078', $existingStatus['port']);
    }

    /**
     * Test that new service provides info about its placeholder status
     */
    public function test_new_service_provides_placeholder_info()
    {
        $newService = app(CvSecurityService::class);
        $info = $newService->getInfo();

        $this->assertEquals('CV Security', $info['service']);
        $this->assertEquals('placeholder', $info['status']);
        $this->assertArrayHasKey('config', $info);
        $this->assertArrayHasKey('host', $info['config']);
        $this->assertArrayHasKey('port', $info['config']);
        $this->assertArrayHasKey('has_api_key', $info['config']);
    }

    /**
     * Test that existing service maintains its functionality
     */
    public function test_existing_service_maintains_functionality()
    {
        $existingService = app(CvSecurityServiceExt::class);

        // Test that key methods exist and return expected types
        $this->assertTrue(method_exists($existingService, 'isServiceAvailable'));
        $this->assertTrue(method_exists($existingService, 'getServiceStatus'));
        $this->assertTrue(method_exists($existingService, 'createSukh'));
        $this->assertTrue(method_exists($existingService, 'createBuilding'));
        $this->assertTrue(method_exists($existingService, 'createEntrance'));

        // Test service status structure
        $status = $existingService->getServiceStatus();
        $this->assertArrayHasKey('service', $status);
        $this->assertArrayHasKey('status', $status);
        $this->assertArrayHasKey('host', $status);
        $this->assertArrayHasKey('port', $status);
    }
}
