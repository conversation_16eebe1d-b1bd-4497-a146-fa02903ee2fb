{"openapi": "3.1.0", "info": {"title": "CVSecurity API", "description": "CV Security API with automatic token authentication", "version": "0.1.0"}, "servers": [{"url": "http://127.0.0.1:8000", "description": "Local server"}], "paths": {"/ele_levels/": {"post": {"tags": ["ele_levels"], "summary": "Create Ele Level", "description": "Create a new elevator level.", "operationId": "create_ele_level_ele_levels__post", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EleLevelCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EleLevel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["ele_levels"], "summary": "Read Ele Levels", "description": "Retrieve a list of elevator levels.", "operationId": "read_ele_levels_ele_levels__get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/EleLevel"}, "title": "Response Read Ele Levels Ele Levels  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/ele_levels/{level_id}": {"get": {"tags": ["ele_levels"], "summary": "Read Ele Level", "description": "Retrieve a specific elevator level by ID.", "operationId": "read_ele_level_ele_levels__level_id__get", "security": [{"BearerAuth": []}], "parameters": [{"name": "level_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Level Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EleLevel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["ele_levels"], "summary": "Update Ele Level", "description": "Update an elevator level.", "operationId": "update_ele_level_ele_levels__level_id__put", "security": [{"BearerAuth": []}], "parameters": [{"name": "level_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Level Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EleLevelUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EleLevel"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["ele_levels"], "summary": "Delete Ele Level", "description": "Delete an elevator level.", "operationId": "delete_ele_level_ele_levels__level_id__delete", "security": [{"BearerAuth": []}], "parameters": [{"name": "level_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Level Id"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/sukhs/": {"post": {"tags": ["sukhs"], "summary": "Create <PERSON><PERSON>", "description": "Create a new Sukh (Residents' Committee) in both Area and Department tables.\n\nThis endpoint creates identical entities in both tables with the same data,\nbut with table-specific fields set appropriately.\n\nThe code field is automatically generated and follows the format SUKH_XXXX.", "operationId": "create_sukh_sukhs__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/sukhs/next-code": {"get": {"tags": ["sukhs"], "summary": "Get Next Sukh Code", "description": "Get the next available Sukh code.\n\nThis code will be used for both Area and Department tables to ensure consistency.", "operationId": "get_next_sukh_code_sukhs_next_code_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Get Next Sukh Code Sukhs Next Code Get"}}}}}, "security": [{"BearerAuth": []}]}}, "/sukhs/{code}": {"put": {"tags": ["sukhs"], "summary": "Update Su<PERSON>", "description": "Update a Sukh (Residents' Committee) in both Area and Department tables.\n\nThis endpoint updates identical entities in both tables with the same data,\nbut with table-specific fields set appropriately.\n\nThe Sukh is identified by its code (e.g., SUKH_0001).", "operationId": "update_sukh_sukhs__code__put", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["sukhs"], "summary": "Get Sukh By Code", "description": "Get a Sukh (Residents' Committee) by its code from both Area and Department tables.\n\nThe Sukh is identified by its code (e.g., SUKH_0001).\nReturns both the Area and Department entities.", "operationId": "get_sukh_by_code_sukhs__code__get", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["sukhs"], "summary": "Delete Sukh", "description": "Delete a Sukh (Residents' Committee) from both Area and Department tables.\n\nThe Sukh is identified by its code (e.g., SUKH_0001).", "operationId": "delete_sukh_sukhs__code__delete", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/sukhs/area/list": {"get": {"tags": ["sukhs"], "summary": "Get Area Sukhs", "description": "Get a list of Sukhs (Residents' Committees) from the Area table.\n\nThis endpoint returns only the Area entities, not the Department entities.", "operationId": "get_area_sukhs_sukhs_area_list_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AreaResponse"}, "title": "Response Get Area Sukhs Sukhs Area List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/sukhs/department/list": {"get": {"tags": ["sukhs"], "summary": "Get Department Sukhs", "description": "Get a list of Sukhs (Residents' Committees) from the Department table.\n\nThis endpoint returns only the Department entities, not the Area entities.", "operationId": "get_department_sukhs_sukhs_department_list_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentResponse"}, "title": "Response Get Department Sukhs Sukhs Department List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/buildings/": {"post": {"tags": ["buildings"], "summary": "Create Building", "description": "Create a new Building in both Area and Department tables.\n\nThis endpoint creates identical entities in both tables with the same data,\nbut with table-specific fields set appropriately.\n\nYou can specify either:\n- parent_code: The code of the parent Sukh (e.g., SUKH_0001)\n- parent_id: The ID of the parent Sukh (for backward compatibility)\n\nIf both are provided, parent_code takes precedence.\n\nThe code field is automatically generated and follows the format BLDG_XXXXX.", "operationId": "create_building_buildings__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/buildings/next-code": {"get": {"tags": ["buildings"], "summary": "Get Next Building Code", "description": "Get the next available Building code.\n\nThis code will be used for both Area and Department tables to ensure consistency.", "operationId": "get_next_building_code_buildings_next_code_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Get Next Building Code Buildings Next Code Get"}}}}}, "security": [{"BearerAuth": []}]}}, "/buildings/{code}": {"put": {"tags": ["buildings"], "summary": "Update Building", "description": "Update a Building in both Area and Department tables.\n\nThis endpoint updates identical entities in both tables with the same data,\nbut with table-specific fields set appropriately.\n\nThe Building is identified by its code (e.g., BLDG_00001).\nThe parent_id must be the ID of a Sukh that exists in both tables.", "operationId": "update_building_buildings__code__put", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["buildings"], "summary": "Get Building By Code", "description": "Get a Building by its code from both Area and Department tables.\n\nThe Building is identified by its code (e.g., BLDG_00001).\nReturns both the Area and Department entities.", "operationId": "get_building_by_code_buildings__code__get", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["buildings"], "summary": "Delete Building", "description": "Delete a Building from both Area and Department tables.\n\nThe Building is identified by its code (e.g., BLDG_00001).", "operationId": "delete_building_buildings__code__delete", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/buildings/area/list": {"get": {"tags": ["buildings"], "summary": "Get Area Buildings", "description": "Get a list of Buildings from the Area table.\n\nThis endpoint returns only the Area entities, not the Department entities.", "operationId": "get_area_buildings_buildings_area_list_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AreaResponse"}, "title": "Response Get Area Buildings Buildings Area List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/buildings/department/list": {"get": {"tags": ["buildings"], "summary": "Get Department Buildings", "description": "Get a list of Buildings from the Department table.\n\nThis endpoint returns only the Department entities, not the Area entities.", "operationId": "get_department_buildings_buildings_department_list_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentResponse"}, "title": "Response Get Department Buildings Buildings Department List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/entrances/": {"post": {"tags": ["entrances"], "summary": "Create Entrance", "description": "Create a new Entrance in both Area and Department tables.\n\nThis endpoint creates identical entities in both tables with the same data,\nbut with table-specific fields set appropriately.\n\nYou can specify either:\n- parent_code: The code of the parent Building (e.g., BLDG_00001)\n- parent_id: The ID of the parent Building (for backward compatibility)\n\nIf both are provided, parent_code takes precedence.\n\nThe code field is automatically generated and follows the format ENTR_XXXXXX.", "operationId": "create_entrance_entrances__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityCreate"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"BearerAuth": []}]}}, "/entrances/next-code": {"get": {"tags": ["entrances"], "summary": "Get Next Entrance Code", "description": "Get the next available Entrance code.\n\nThis code will be used for both Area and Department tables to ensure consistency.", "operationId": "get_next_entrance_code_entrances_next_code_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Get Next Entrance Code Entrances Next Code Get"}}}}}, "security": [{"BearerAuth": []}]}}, "/entrances/{code}": {"put": {"tags": ["entrances"], "summary": "Update Entrance", "description": "Update an Entrance in both Area and Department tables.\n\nThis endpoint updates identical entities in both tables with the same data,\nbut with table-specific fields set appropriately.\n\nThe Entrance is identified by its code (e.g., ENTR_000001).\nThe parent_id must be the ID of a Building that exists in both tables.", "operationId": "update_entrance_entrances__code__put", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["entrances"], "summary": "Get Entrance By Code", "description": "Get an Entrance by its code from both Area and Department tables.\n\nThe Entrance is identified by its code (e.g., ENTR_000001).\nReturns both the Area and Department entities.", "operationId": "get_entrance_by_code_entrances__code__get", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TiedEntityResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["entrances"], "summary": "Delete Entrance", "description": "Delete an Entrance from both Area and Department tables.\n\nThe Entrance is identified by its code (e.g., ENTR_000001).", "operationId": "delete_entrance_entrances__code__delete", "security": [{"BearerAuth": []}], "parameters": [{"name": "code", "in": "path", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"204": {"description": "Successful Response"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/entrances/area/list": {"get": {"tags": ["entrances"], "summary": "Get Area Entrances", "description": "Get a list of Entrances from the Area table.\n\nThis endpoint returns only the Area entities, not the Department entities.", "operationId": "get_area_entrances_entrances_area_list_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AreaResponse"}, "title": "Response Get Area Entrances Entrances Area List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/entrances/department/list": {"get": {"tags": ["entrances"], "summary": "Get Department Entrances", "description": "Get a list of Entrances from the Department table.\n\nThis endpoint returns only the Department entities, not the Area entities.", "operationId": "get_department_entrances_entrances_department_list_get", "security": [{"BearerAuth": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DepartmentResponse"}, "title": "Response Get Department Entrances Entrances Department List Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/persons/next-pin": {"get": {"tags": ["persons"], "summary": "Get Next Person Pin", "description": "Get the next available Person PIN.\n\nThis PIN will be a numeric string that is unique across all persons.", "operationId": "get_next_person_pin_persons_next_pin_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"additionalProperties": {"type": "string"}, "type": "object", "title": "Response Get Next Person Pin Persons Next Pin Get"}}}}}, "security": [{"BearerAuth": []}]}}, "/": {"get": {"summary": "Index", "operationId": "index__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"AreaResponse": {"properties": {"name": {"type": "string", "title": "Name", "index": true}, "remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Remark"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id", "foreign_key": "auth_area.id"}, "app_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Id"}, "bio_tbl_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bio Tbl Id"}, "company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Id"}, "init_flag": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Init Flag"}, "id": {"type": "string", "title": "Id"}, "code": {"type": "string", "title": "Code"}, "create_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Create Time"}, "update_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Update Time"}, "op_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Op Version"}, "creater_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creater Code"}, "creater_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>reater Id"}, "creater_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creater Name"}, "updater_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Code"}, "updater_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Id"}, "updater_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Name"}}, "type": "object", "required": ["name", "id", "code"], "title": "AreaResponse", "description": "Response model for Area API endpoints."}, "DepartmentResponse": {"properties": {"name": {"type": "string", "title": "Name", "index": true}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id", "foreign_key": "auth_department.id"}, "app_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "App Id"}, "bio_tbl_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bio Tbl Id"}, "company_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Company Id"}, "init_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Init Code"}, "sort": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Sort"}, "id": {"type": "string", "title": "Id"}, "code": {"type": "string", "title": "Code"}, "create_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Create Time"}, "update_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Update Time"}, "op_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Op Version"}, "creater_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creater Code"}, "creater_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>reater Id"}, "creater_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creater Name"}, "updater_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Code"}, "updater_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Id"}, "updater_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Name"}}, "type": "object", "required": ["name", "id", "code"], "title": "DepartmentResponse", "description": "Response model for Department API endpoints."}, "EleLevel": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "auth_area_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Auth Area Id"}, "timeseg_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>d"}, "id": {"type": "string", "title": "Id"}, "create_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Create Time"}, "update_time": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Update Time"}, "creater_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creater Code"}, "creater_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON>reater Id"}, "creater_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Creater Name"}, "updater_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Code"}, "updater_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Id"}, "updater_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Updater Name"}, "op_version": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Op Version"}}, "type": "object", "required": ["id"], "title": "EleLevel"}, "EleLevelCreate": {"properties": {"name": {"type": "string", "title": "Name"}, "auth_area_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Auth Area Id"}, "timeseg_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>d"}}, "type": "object", "required": ["name"], "title": "EleLevelCreate"}, "EleLevelUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "auth_area_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Auth Area Id"}, "timeseg_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>d"}}, "type": "object", "title": "EleLevelUpdate"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "TiedEntityCreate": {"properties": {"name": {"type": "string", "title": "Name", "index": true}, "remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Remark"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}, "parent_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Code"}, "code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}}, "type": "object", "required": ["name"], "title": "TiedEntityCreate", "description": "Schema for creating a new tied entity."}, "TiedEntityResponse": {"properties": {"area": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Area"}, "department": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Department"}}, "type": "object", "title": "TiedEntityResponse", "description": "Response schema for tied entities."}, "TiedEntityUpdate": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "remark": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Remark"}, "parent_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Id"}, "parent_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Parent Code"}}, "type": "object", "title": "TiedEntityUpdate", "description": "Schema for updating a tied entity."}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "Enter: 123456"}}}, "tags": [{"name": "sukhs", "description": "Operations with Sukhs in both Area and Department tables"}, {"name": "buildings", "description": "Operations with Buildings in both Area and Department tables"}, {"name": "entrances", "description": "Operations with Entrances in both Area and Department tables"}, {"name": "ele_levels", "description": "Operations with Elevator Levels"}, {"name": "persons", "description": "Operations with Persons"}], "x-tagGroups": [{"name": "Entity Endpoints", "tags": ["sukhs", "buildings", "entrances"]}, {"name": "Other Endpoints", "tags": ["ele_levels", "persons"]}]}