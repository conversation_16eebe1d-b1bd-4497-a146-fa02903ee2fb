<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Orc extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'     => $this->id,
            'number' => $this->number,
            'code'   => $this->code,
            'korpus' => $this->korpus ? new Korpus($this->korpus) : null,
        ];
    }
}
