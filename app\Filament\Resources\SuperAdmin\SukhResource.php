<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\SukhResource\Pages;

use App\Models\Constant\ConstData;
use App\Models\User;
use App\Models\Sukh;
use App\Models\Aimag;
use App\Services\InfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SukhResource extends Resource
{
    protected static ?string $model = Sukh::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office-2';
    protected static ?string $navigationLabel = 'СӨХ';
    protected static ?string $modelLabel = 'СӨХ';
    protected static ?string $pluralModelLabel = 'СӨХ';
    protected static ?int $navigationSort = 2;
    protected static ?string $slug = 'sukhs';
    protected static ?string $navigationGroup = 'Бүртгэл';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Нэр')
                            ->maxValue(50)
                            ->required(),

                        Forms\Components\TextInput::make('registration_number')
                            ->label('Регистрийн дугаар')
                            ->unique(ignoreRecord: true)
                            ->maxValue(7)
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label('Гар утас')
                            ->tel()
                            ->telRegex('/^[+]*[(]{0,1}[0-9]{1,4}[)]{0,1}[-\s\.\/0-9]*$/')
                            ->required()
                            ->maxValue(8),

                        Forms\Components\TextInput::make('email')
                            ->label('Имэйл хаяг')
                            ->email()
                            ->unique(ignoreRecord: true),

                        Forms\Components\TextInput::make('code')
                            ->label('CV Security код')
                            ->disabled()
                            ->dehydrated(false)
                            ->helperText('Энэ талбар автоматаар CV Security системээс ирнэ'),

                        Forms\Components\Select::make('users')
                            ->label('Хэрэглэгч')
                            ->relationship(name: 'users', titleAttribute: 'name')
                            ->options(User::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->searchable()
                            ->required(),

                        Forms\Components\Select::make('aimag_id')
                            ->label('Аймаг/Хот')
                            ->options(Aimag::all()->pluck(ConstData::NAME, ConstData::ID))
                            ->default(function (callable $get) {
                                    $service = resolve(InfoService::class);
                                    return $service->getUBId();
                                })
                            ->searchable(),

                        Forms\Components\Select::make('soum_id')
                            ->label('Сум/Дүүрэг')
                            ->options(
                                function (callable $get) {
                                    $service = resolve(InfoService::class);
                                    return $service->getSoums($get('aimag_id'));
                                }
                            )
                            ->placeholder('Сонгох')
                            ->searchable(),

                        Forms\Components\Select::make('bag_id')
                            ->label('Баг/Хороо')
                            ->options(
                                function (callable $get) {
                                    $service = resolve(InfoService::class);
                                    return $service->getBags($get('aimag_id'), $get('soum_id'));
                                }
                            )
                            ->placeholder('Сонгох')
                            ->searchable(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Sukh $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Sukh $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Sukh $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Sukh $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('registration_number')->label('Регистрийн дугаар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('phone')->label('Гар утас')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('email')->label('Имэйл хаяг')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('code')
                    ->label('CV Security код')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {
                        '' => 'gray',
                        default => 'success',
                    })
                    ->formatStateUsing(fn (string $state): string => $state ?: 'Байхгүй')
                    ->sortable(),
                Tables\Columns\TextColumn::make('aimag.name')->label('Аймаг/Хот')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('soum.name')->label('Сум/Дүүрэг')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('bag.name')->label('Баг/Хороо')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSukhs::route('/'),
            'create' => Pages\CreateSukh::route('/create'),
            'edit' => Pages\EditSukh::route('/{record}/edit'),
        ];
    }
}
