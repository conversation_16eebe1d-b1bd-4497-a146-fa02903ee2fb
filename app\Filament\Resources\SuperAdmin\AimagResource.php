<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\AimagResource\Pages;
use App\Filament\Resources\SuperAdmin\AimagResource\RelationManagers;
use App\Models\Aimag;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AimagResource extends Resource
{
    protected static ?string $model = Aimag::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Аймаг/Хот';
    protected static ?string $modelLabel = 'Аймаг/Хот';
    protected static ?int $navigationSort = 100;
    protected static ?string $slug = 'aimags';
    protected static ?string $navigationGroup = 'Инфо';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Нэр')
                            ->maxValue(50)
                            ->required(),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Aimag $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (Aimag $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (Aimag $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Aimag $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAimags::route('/'),
            'create' => Pages\CreateAimag::route('/create'),
            'edit' => Pages\EditAimag::route('/{record}/edit'),
        ];
    }
}
