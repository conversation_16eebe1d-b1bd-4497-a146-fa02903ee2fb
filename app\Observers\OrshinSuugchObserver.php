<?php

namespace App\Observers;

use App\Models\OrshinSuugch;
use App\Services\OrshinSuugchSyncService;
use Illuminate\Support\Facades\Log;

/**
 * OrshinSuugch Observer
 * 
 * This observer listens to OrshinSuugch model events and triggers
 * appropriate CVSecurity synchronization operations.
 */
class OrshinSuugchObserver
{
    protected OrshinSuugchSyncService $orshinSuugchSyncService;

    public function __construct(OrshinSuugchSyncService $orshinSuugchSyncService)
    {
        $this->orshinSuugchSyncService = $orshinSuugchSyncService;
    }

    /**
     * Handle the OrshinSuugch "created" event.
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function created(OrshinSuugch $orshinSuugch): void
    {
        Log::info('OrshinSuugchObserver: OrshinSuugch created event triggered', [
            'orshin_suugch_id' => $orshinSuugch->id,
            'name' => $orshinSuugch->name,
            'phone' => $orshinSuugch->phone
        ]);

        // Sync with CVSecurity service
        $this->orshinSuugchSyncService->syncCreate($orshinSuugch);
    }

    /**
     * Handle the OrshinSuugch "updated" event.
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function updated(OrshinSuugch $orshinSuugch): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($orshinSuugch->wasChanged(OrshinSuugch::CODE) && count($orshinSuugch->getChanges()) === 1) {
            Log::debug('OrshinSuugchObserver: Skipping sync for code-only update', [
                'orshin_suugch_id' => $orshinSuugch->id
            ]);
            return;
        }

        Log::info('OrshinSuugchObserver: OrshinSuugch updated event triggered', [
            'orshin_suugch_id' => $orshinSuugch->id,
            'name' => $orshinSuugch->name,
            'phone' => $orshinSuugch->phone,
            'changed_fields' => array_keys($orshinSuugch->getChanges())
        ]);

        // Sync with CVSecurity service
        $this->orshinSuugchSyncService->syncUpdate($orshinSuugch);
    }

    /**
     * Handle the OrshinSuugch "deleted" event.
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function deleted(OrshinSuugch $orshinSuugch): void
    {
        Log::info('OrshinSuugchObserver: OrshinSuugch deleted event triggered', [
            'orshin_suugch_id' => $orshinSuugch->id,
            'name' => $orshinSuugch->name,
            'phone' => $orshinSuugch->phone,
            'cv_code' => $orshinSuugch->code
        ]);

        // Sync with CVSecurity service
        $this->orshinSuugchSyncService->syncDelete($orshinSuugch);
    }

    /**
     * Handle the OrshinSuugch "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param OrshinSuugch $orshinSuugch
     * @return void
     */
    public function retrieved(OrshinSuugch $orshinSuugch): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->orshinSuugchSyncService->syncRead($orshinSuugch);
    }
}
