<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\ContractTemplate
 *
 * @mixin IdeHelperContractTemplate
 * @property int $id
 * @property string $name
 * @property int $contract_template_type_id
 * @property string|null $file_path
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\ContractTemplateType|null $contract_template_type
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate whereContractTemplateTypeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate whereFilePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|ContractTemplate whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class ContractTemplate extends Model
{
    use HasFactory;
    const TABLE                     = 'contract_templates';

    const ID                        = 'id';
    const NAME                      = 'name';
    const CONTRACT_TEMPLATE_TYPE_ID = 'contract_template_type_id';
    const FILE_PATH                 = 'file_path';

    const ATTACHMENTS               = 'attachments';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::NAME,
        self::CONTRACT_TEMPLATE_TYPE_ID,
        self::FILE_PATH,
    ];

    public function contract_template_type(): BelongsTo
    {
        return $this->belongsTo(ContractTemplateType::class);
    }
}
