<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\QpayToken
 *
 * @mixin IdeHelperQpayToken
 * @property int $id
 * @property int $orshin_suugch_id
 * @property string $token_type
 * @property string $refresh_expires_in
 * @property string $refresh_token
 * @property string $access_token
 * @property string $expires_in
 * @property string $scope
 * @property string $session_state
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\OrshinSuugch|null $orshin_suugch
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken query()
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereAccessToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereExpiresIn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereRefreshExpiresIn($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereRefreshToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereScope($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereSessionState($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereTokenType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|QpayToken whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class QpayToken extends Model
{
    use HasFactory;
    const ID                 = 'id';
    const ORSHIN_SUUGCH_ID   = 'orshin_suugch_id';
    const TOKEN_TYPE         = 'token_type';
    const REFRESH_EXPIRES_IN = 'refresh_expires_in';
    const REFRESH_TOKEN      = 'refresh_token';
    const ACCESS_TOKEN       = 'access_token';
    const EXPIRES_IN         = 'expires_in';
    const SCOPE              = 'scope';
    const SESSION_STATE      = 'session_state';

    const RELATION_ORSHIN_SUUGCH = 'orshin_suugch';

    protected $fillable = [
        self::ID,
        self::ORSHIN_SUUGCH_ID,
        self::TOKEN_TYPE,
        self::REFRESH_EXPIRES_IN,
        self::REFRESH_TOKEN,
        self::ACCESS_TOKEN,
        self::EXPIRES_IN,
        self::SCOPE,
        self::SESSION_STATE,
    ];

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }

}
