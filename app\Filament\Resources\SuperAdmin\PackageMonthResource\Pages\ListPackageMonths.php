<?php

namespace App\Filament\Resources\SuperAdmin\PackageMonthResource\Pages;

use App\Filament\Resources\SuperAdmin\PackageMonthResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPackageMonths extends ListRecords
{
    protected static string $resource = PackageMonthResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
