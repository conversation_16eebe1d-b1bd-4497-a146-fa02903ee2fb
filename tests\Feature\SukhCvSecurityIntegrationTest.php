<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Services\CvSecurityService\CvSecurityService;
use App\Services\SukhSyncService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class SukhCvSecurityIntegrationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected CvSecurityService $cvSecurityService;
    protected SukhSyncService $sukhSyncService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->cvSecurityService = app(CvSecurityService::class);
        $this->sukhSyncService = app(SukhSyncService::class);
    }

    /** @test */
    public function it_can_check_cv_security_service_status()
    {
        $status = $this->cvSecurityService->getServiceStatus();

        $this->assertIsArray($status);
        $this->assertArrayHasKey('service', $status);
        $this->assertArrayHasKey('status', $status);
        $this->assertArrayHasKey('host', $status);
        $this->assertArrayHasKey('port', $status);
        $this->assertEquals('CV Security', $status['service']);
    }

    /** @test */
    public function it_automatically_syncs_sukh_creation_with_cv_security()
    {
        // Skip if CVSecurity service is not available
        if (!$this->cvSecurityService->isServiceAvailable()) {
            $this->markTestSkipped('CVSecurity service is not available');
        }

        // Create a Sukh
        $sukh = Sukh::create([
            'name' => 'Test СӨХ Integration',
            'registration_number' => 'T123456',
            'phone' => '99887766',
            'email' => '<EMAIL>',
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ]);

        // Refresh to get the updated code
        $sukh->refresh();

        // Assert that the code was assigned
        $this->assertNotNull($sukh->code);
        $this->assertIsString($sukh->code);
        $this->assertStringStartsWith('SUKH_', $sukh->code);
    }

    /** @test */
    public function it_automatically_syncs_sukh_update_with_cv_security()
    {
        // Skip if CVSecurity service is not available
        if (!$this->cvSecurityService->isServiceAvailable()) {
            $this->markTestSkipped('CVSecurity service is not available');
        }

        // Create a Sukh first
        $sukh = Sukh::create([
            'name' => 'Test СӨХ Update',
            'registration_number' => 'T654321',
            'phone' => '99112233',
            'email' => '<EMAIL>',
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ]);

        $sukh->refresh();
        $originalCode = $sukh->code;

        // Update the Sukh
        $sukh->update([
            'name' => 'Updated Test СӨХ',
            'phone' => '99445566',
        ]);

        $sukh->refresh();

        // Assert that the code is still present (should remain the same)
        $this->assertNotNull($sukh->code);
        $this->assertEquals($originalCode, $sukh->code);
    }

    /** @test */
    public function it_automatically_syncs_sukh_deletion_with_cv_security()
    {
        // Skip if CVSecurity service is not available
        if (!$this->cvSecurityService->isServiceAvailable()) {
            $this->markTestSkipped('CVSecurity service is not available');
        }

        // Create a Sukh first
        $sukh = Sukh::create([
            'name' => 'Test СӨХ Delete',
            'registration_number' => 'T789012',
            'phone' => '99778899',
            'email' => '<EMAIL>',
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ]);

        $sukh->refresh();
        $sukhId = $sukh->id;
        $sukhCode = $sukh->code;

        // Assert that the code was assigned
        $this->assertNotNull($sukhCode);

        // Delete the Sukh
        $sukh->delete();

        // Assert that the Sukh is deleted from database
        $this->assertDatabaseMissing('sukhs', ['id' => $sukhId]);
    }

    /** @test */
    public function it_handles_cv_security_service_unavailable_gracefully()
    {
        // Mock the CVSecurity service to be unavailable
        $mockService = $this->createMock(CvSecurityService::class);
        $mockService->method('isServiceAvailable')->willReturn(false);

        // Mock the SukhSyncService as well
        $mockSyncService = $this->createMock(SukhSyncService::class);
        $mockSyncService->expects($this->once())
                       ->method('syncCreate')
                       ->willReturnCallback(function($sukh) {
                           // Simulate service unavailable - don't set code
                           $sukh->updateQuietly(['code' => null]);
                       });

        $this->app->instance(CvSecurityService::class, $mockService);
        $this->app->instance(SukhSyncService::class, $mockSyncService);

        // Create a Sukh
        $sukh = Sukh::create([
            'name' => 'Test СӨХ Offline',
            'registration_number' => 'T999888',
            'phone' => '99000111',
            'email' => '<EMAIL>',
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ]);

        $sukh->refresh();

        // Assert that the code is null when service is unavailable
        $this->assertNull($sukh->code);
    }

    /** @test */
    public function sukh_model_has_code_field_in_fillable()
    {
        $sukh = new Sukh();
        $fillable = $sukh->getFillable();

        $this->assertContains('code', $fillable);
    }

    /** @test */
    public function sukh_model_has_code_constant()
    {
        $this->assertEquals('code', Sukh::CODE);
    }

    /** @test */
    public function it_can_extract_code_from_cv_security_response()
    {
        $response = (object) [
            'area' => (object) [
                'code' => 'SUKH_TEST_001'
            ]
        ];

        $reflection = new \ReflectionClass($this->sukhSyncService);
        $method = $reflection->getMethod('extractCodeFromResponse');
        $method->setAccessible(true);

        $code = $method->invoke($this->sukhSyncService, $response);

        $this->assertEquals('SUKH_TEST_001', $code);
    }

    /** @test */
    public function it_returns_null_for_invalid_cv_security_response()
    {
        $response = (object) [
            'invalid' => 'data'
        ];

        $reflection = new \ReflectionClass($this->sukhSyncService);
        $method = $reflection->getMethod('extractCodeFromResponse');
        $method->setAccessible(true);

        $code = $method->invoke($this->sukhSyncService, $response);

        $this->assertNull($code);
    }
}
