<?php

namespace App\Filament\Resources\SuperAdmin\AimagResource\Pages;

use App\Filament\Resources\SuperAdmin\AimagResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAimag extends EditRecord
{
    protected static string $resource = AimagResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
