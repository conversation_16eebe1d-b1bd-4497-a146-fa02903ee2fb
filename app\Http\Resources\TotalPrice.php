<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class TotalPrice extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'price'           => $this->price,
            'month_discount'  => $this->month_discount,
            'member_discount' => $this->member_discount,
            'total_price'     => $this->total_price,
        ];
    }
}
