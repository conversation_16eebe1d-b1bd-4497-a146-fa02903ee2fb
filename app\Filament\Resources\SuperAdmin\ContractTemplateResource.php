<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\ContractTemplateResource\Pages;
use App\Models\Constant\ConstData;
use App\Models\ContractTemplate;
use App\Models\ContractTemplateType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\FileUpload;

class ContractTemplateResource extends Resource
{
    protected static ?string $model = ContractTemplate::class;

    protected static ?string $navigationIcon = 'heroicon-o-document-text';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Гэрээний загвар';
    protected static ?string $modelLabel = 'гэрээний загвар';
    protected static ?int $navigationSort = 5;
    protected static ?string $slug = 'contractTemplates';
    protected static ?string $navigationGroup = 'Гэрээ';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                ->schema([
                    Forms\Components\TextInput::make(ContractTemplate::NAME)
                        ->label('Нэр')
                        ->maxValue(50)
                        ->required(),

                    Forms\Components\Select::make(ContractTemplate::CONTRACT_TEMPLATE_TYPE_ID)
                    ->label('Төрөл')
                    ->options(ContractTemplateType::all()->pluck(ConstData::NAME, ConstData::ID))
                    ->required(),

                    FileUpload::make(ContractTemplate::ATTACHMENTS)
                        ->disk('minio')
                        ->visibility('private')
                        ->directory('temp')
                        ->label("Гэрээний загвар файл оруулна уу!")
                        ->downloadable()
                        ->preserveFilenames()
                        ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.wordprocessingml.document'])
                ])
                ->columns(1)
                ->columnSpan(['lg' => fn (?ContractTemplate $record) => $record === null ? 2 : 1]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (ContractTemplate $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (ContractTemplate $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?ContractTemplate $record) => $record === null),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('contract_template_type.name')->label('Загварын төрөл')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([

            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListContractTemplates::route('/'),
            'create' => Pages\CreateContractTemplate::route('/create'),
            'edit' => Pages\EditContractTemplate::route('/{record}/edit'),
        ];
    }
}
