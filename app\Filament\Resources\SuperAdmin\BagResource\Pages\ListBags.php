<?php

namespace App\Filament\Resources\SuperAdmin\BagResource\Pages;

use App\Filament\Resources\SuperAdmin\BagResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBags extends ListRecords
{
    protected static string $resource = BagResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
        ];
    }
}
