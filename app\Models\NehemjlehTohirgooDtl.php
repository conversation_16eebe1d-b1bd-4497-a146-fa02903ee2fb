<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NehemjlehTohirgooDtl
 *
 * @property int $id
 * @property int $nehemjleh_tohirgoo_id
 * @property int $bair_id
 * @property int $korpus_id
 * @property int $number
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Bair|null $bair
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \App\Models\NehemjlehTohirgoo $nehemjleh_tohirgoo
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl query()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereBairId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereNehemjlehTohirgooId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehTohirgooDtl whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class NehemjlehTohirgooDtl extends Model
{
    use HasFactory;

    const ID                    = 'id';
    const NEHEMJLEH_TOHIRGOO_ID = 'nehemjleh_tohirgoo_id';
    const BAIR_ID               = 'bair_id';
    const KORPUS_ID             = 'korpus_id';
    const NUMBER                = 'number';

    protected $fillable = [
        self::ID,
        self::NEHEMJLEH_TOHIRGOO_ID,
        self::BAIR_ID,
        self::KORPUS_ID,
        self::NUMBER
    ];

    public function nehemjleh_tohirgoo()
    {
        return $this->belongsTo(NehemjlehTohirgoo::class);
    }

    public function bair()
    {
        return $this->belongsTo(Bair::class);
    }

    public function korpus()
    {
        return $this->belongsTo(Korpus::class);
    }
}
