<?php

namespace App\Filament\Resources\Admin;

use App\Models\Nehemjleh;
use App\Services\NehemjlehService;

use App\Filament\Resources\Admin\NehemjlehResource\Pages;
use App\Filament\Resources\Admin\NehemjlehResource\RelationManagers;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Support\Enums\FontWeight;

class NehemjlehResource extends Resource
{
    protected static ?string $model = Nehemjleh::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Нэхэмжлэх';
    protected static ?string $modelLabel      = 'Нэхэмжлэх';
    protected static ?int $navigationSort     = 5;
    protected static ?string $slug            = 'nehemjleh';
    protected static ?string $navigationGroup = 'Биллинг';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Nehemjleh::CODE)->label('Нэхэмжлэхийн №')->sortable(),
                Tables\Columns\TextColumn::make(Nehemjleh::YEAR)->label('Жил')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Nehemjleh::MONTH)->label('Сар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Nehemjleh::BAIR_NAME)->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Nehemjleh::KORPUS_NAME)->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Nehemjleh::TOOT_NUMBER)->label('Тоот')->sortable()->searchable()->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make(Nehemjleh::ORSHIN_SUUGCH_OVOG)->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Nehemjleh::ORSHIN_SUUGCH_NER)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Nehemjleh::UTAS)->label('Утас')->sortable()->searchable()->copyable()->weight(FontWeight::Bold)->icon('heroicon-m-phone'),
                Tables\Columns\TextColumn::make(Nehemjleh::NIIT_DUN)->label('Нийт дүн')->sortable()->searchable()->money('MNT'),
                Tables\Columns\TextColumn::make(Nehemjleh::TULSUN_DUN)->label('Төлсөн дүн')->sortable()->searchable()->money('MNT'),
                Tables\Columns\TextColumn::make(Nehemjleh::ULDEGDEL_DUN)->label('Улдэгдэл дүн')->sortable()->searchable()->money('MNT')->weight(FontWeight::Bold),
                Tables\Columns\TextColumn::make(Nehemjleh::STATUS)->label('Төлөв')->badge(),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('Үүсгэх')
                    ->form([
                        Forms\Components\TextInput::make(Nehemjleh::YEAR)
                            ->label('Жил')
                            ->default(date('Y'))
                            ->minValue(2020)
                            ->maxValue(2050)
                            ->integer()
                            ->required(),
                        Forms\Components\TextInput::make(Nehemjleh::MONTH)
                            ->label('Сар')
                            ->default(intval(date('m')))
                            ->minValue(1)
                            ->maxValue(12)
                            ->integer()
                            ->required(),
                    ])->action(function (array $data) {
                        $year               = $data[Nehemjleh::YEAR];
                        $month              = $data[Nehemjleh::MONTH];
                        resolve(NehemjlehService::class)->createNehemjlehByTohirgoo($year, $month);
                    }),
            ])
            ->actions([
            ])
            ->BulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListNehemjlehs::route('/'),
        ];
    }
}
