<?php

namespace App\Filament\Resources\SuperAdmin;

use App\Filament\Resources\SuperAdmin\ZuuchlagchResource\Pages;
use App\Filament\Resources\SuperAdmin\ZuuchlagchResource\RelationManagers;
use App\Models\Zuuchlagch;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class ZuuchlagchResource extends Resource
{
    protected static ?string $model = Zuuchlagch::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel, $pluralModelLabel = 'Зуучлагч';
    protected static ?string $modelLabel = 'зуучлагч';
    protected static ?int $navigationSort = 5;
    protected static ?string $slug = 'zuuchlagches';
    protected static ?string $navigationGroup = 'Гэрээ';

    public static function form(Form $form): Form
    {
        return $form
        ->schema([
            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\TextInput::make('last_name')
                        ->label('Овог')
                        ->maxValue(50)
                        ->required(),

                    Forms\Components\TextInput::make('first_name')
                        ->label('Нэр')
                        ->maxValue(50)
                        ->required(),

                    Forms\Components\TextInput::make('phone')
                        ->label('Гар утас')
                        ->required()
                        ->tel()
                        ->unique()
                        ->maxValue(8),
                ])
                ->columns(1)
                ->columnSpan(['lg' => fn (?Zuuchlagch $record) => $record === null ? 3 : 2]),

            Forms\Components\Section::make()
                ->schema([
                    Forms\Components\Placeholder::make('created_at')
                        ->label('Created at')
                        ->content(fn (Zuuchlagch $record): ?string => $record->created_at?->diffForHumans()),

                    Forms\Components\Placeholder::make('updated_at')
                        ->label('Last modified at')
                        ->content(fn (Zuuchlagch $record): ?string => $record->updated_at?->diffForHumans()),
                ])
                ->columnSpan(['lg' => 1])
                ->hidden(fn (?Zuuchlagch $record) => $record === null),
        ])
        ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('last_name')->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('first_name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('phone')->label('Утас')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListZuuchlagches::route('/'),
            'create' => Pages\CreateZuuchlagch::route('/create'),
            'edit' => Pages\EditZuuchlagch::route('/{record}/edit'),
        ];
    }
}
