<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('toots', function (Blueprint $table) {
            $table->foreignId('davhar_id')->nullable()->constrained('davhars')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('toots', function (Blueprint $table) {
            $table->dropForeign(['davhar_id']);
            $table->dropColumn('davhar_id');
        });
    }
};
