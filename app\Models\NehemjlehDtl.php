<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\NehemjlehDtl
 *
 * @property int $id
 * @property int $nehemjleh_id
 * @property string $uilchilgee_ner
 * @property string $dun
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Nehemjleh $nehemjleh
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl query()
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl whereDun($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl whereNehemjlehId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl whereUilchilgeeNer($value)
 * @method static \Illuminate\Database\Eloquent\Builder|NehemjlehDtl whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class NehemjlehDtl extends Model
{
    use HasFactory;

    const ID             = 'id';
    const NEHEMJLEH_ID   = 'nehemjleh_id';
    const UILCHILGEE_NER = 'uilchilgee_ner';
    const DUN            = 'dun';

    const RELATION_NEHEMJLEH = 'nehemjleh';

    protected $fillable = [
        self::NEHEMJLEH_ID,
        self::UILCHILGEE_NER,
        self::DUN,
    ];

    public function nehemjleh()
    {
        return $this->belongsTo(Nehemjleh::class);
    }
}
