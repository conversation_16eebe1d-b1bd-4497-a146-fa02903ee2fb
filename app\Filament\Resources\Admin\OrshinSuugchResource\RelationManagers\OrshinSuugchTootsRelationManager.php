<?php

namespace App\Filament\Resources\Admin\OrshinSuugchResource\RelationManagers;

use App\Models\Korpus;
use App\Models\OrshinSuugchToot;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Validation\Rules\Unique;

class OrshinSuugchTootsRelationManager extends RelationManager
{
    protected static string $relationship = 'orshin_suugch_toots';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Select::make(OrshinSuugchToot::BAIR_ID)
                    ->label('Байр')
                    ->options(function (callable $get) {
                        $service = resolve(UserInfoService::class);
                        return $service->getAUBairs();
                    })
                    ->live()
                    ->searchable(),
                Forms\Components\Select::make(OrshinSuugchToot::KORPUS_ID)
                    ->label('Блок')
                    ->options(function (callable $get) {
                        $service = resolve(UserInfoService::class);
                        return $service->getAUKorpuses($get(OrshinSuugchToot::BAIR_ID));
                    })
                    ->live()
                    ->searchable(),

                Forms\Components\Select::make(OrshinSuugchToot::NUMBER)
                    ->label('Тоот')
                    ->unique(
                        ignoreRecord: true,
                        modifyRuleUsing: function (Unique $rule) {
                            $orshinSuugchId = $this->getOwnerRecord()->id;
                            return $rule->where('orshin_suugch_id', $orshinSuugchId);
                        }
                    )
                    ->options(function (callable $get) {
                        $service = resolve(UserInfoService::class);
                        return $service->getAUTootsKeyNumber($get(OrshinSuugchToot::BAIR_ID), $get(OrshinSuugchToot::KORPUS_ID));
                    })
                    ->searchable(),

                Forms\Components\TextInput::make(OrshinSuugchToot::STATE_BANK_CODE)
                    ->label('Төрийн банкны код')
                    ->placeholder('****************')
                    ->length(16),
            ])->columns(1);
    }

    public function table(Table $table): Table
    {
        return $table
            ->heading('Тоот')
            ->columns([
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр'),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок'),
                Tables\Columns\TextColumn::make(OrshinSuugchToot::NUMBER)->label('Тоот'),
                Tables\Columns\TextColumn::make(OrshinSuugchToot::STATE_BANK_CODE)->label('Төрийн банкны код'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->label('Нэмэх'),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->mutateRecordDataUsing(function (array $data): array {
                        $korpus = Korpus::find($data[OrshinSuugchToot::KORPUS_ID]);
                        $data[OrshinSuugchToot::BAIR_ID] = $korpus->bair_id;
                        return $data;
                    }),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
            ]);
    }
}
