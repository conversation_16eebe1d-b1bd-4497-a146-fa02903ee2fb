<?php

namespace App\Console\Commands;

use App\Models\Orc;
use App\Models\Korpus;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use App\Services\OrcSyncService;
use Illuminate\Console\Command;

class TestOrcCvSecurityIntegration extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:orc-cv-security {action=status : The action to perform (status|create|update|delete)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Orc CVSecurity integration';

    protected CvSecurityServiceExt $cvSecurityService;
    protected OrcSyncService $orcSyncService;

    public function __construct(CvSecurityServiceExt $cvSecurityService, OrcSyncService $orcSyncService)
    {
        parent::__construct();
        $this->cvSecurityService = $cvSecurityService;
        $this->orcSyncService = $orcSyncService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        switch ($action) {
            case 'status':
                $this->testServiceStatus();
                break;
            case 'create':
                $this->testCreateOrc();
                break;
            case 'update':
                $this->testUpdateOrc();
                break;
            case 'delete':
                $this->testDeleteOrc();
                break;
            default:
                $this->error("Unknown action: {$action}");
                $this->info('Available actions: status, create, update, delete');
        }
    }

    protected function testServiceStatus()
    {
        $this->info('Testing CVSecurity service status...');

        $status = $this->cvSecurityService->getServiceStatus();

        $this->table(
            ['Property', 'Value'],
            [
                ['Service', $status['service']],
                ['Status', $status['status']],
                ['Host', $status['host']],
                ['Port', $status['port']],
            ]
        );

        if ($status['status'] === 'online') {
            $this->info('✅ CVSecurity service is available');

            // Test getting next entrance code
            $nextCode = $this->cvSecurityService->getNextEntranceCode();
            if ($nextCode) {
                $this->info("✅ Next available entrance code: {$nextCode}");
            } else {
                $this->warn("⚠️ Could not get next entrance code");
            }
        } else {
            $this->error('❌ CVSecurity service is not available');
        }
    }

    protected function testCreateOrc()
    {
        $this->info('Testing Orc creation with CVSecurity integration...');

        // Find a korpus with a code for testing
        $korpus = Korpus::whereNotNull('code')->first();

        if (!$korpus) {
            $this->error('❌ No Korpus with CVSecurity code found. Please sync Korpus first.');
            return;
        }

        $this->info("Using Korpus: {$korpus->name} (Code: {$korpus->code})");

        // Create a test Orc
        $orc = Orc::create([
            'korpus_id' => $korpus->id,
            'number' => 'TEST-' . now()->format('His'),
            'begin_toot_number' => 1,
            'end_toot_number' => 10,
        ]);

        $this->info("Created Orc with ID: {$orc->id}");

        // Wait a moment for the observer to process
        sleep(2);

        // Refresh the model to get the updated code
        $orc->refresh();

        if ($orc->code) {
            $this->info("✅ CVSecurity code assigned: {$orc->code}");
        } else {
            $this->warn("⚠️ No CVSecurity code assigned (service might be unavailable)");
        }

        // Test reading from CVSecurity
        if ($orc->code) {
            $cvData = $this->cvSecurityService->getEntranceByCode($orc->code);
            if ($cvData) {
                $this->info("✅ Successfully read entrance from CVSecurity");
                $this->line("Name: " . ($cvData->area->name ?? 'N/A'));
                $this->line("Remark: " . ($cvData->area->remark ?? 'N/A'));
            } else {
                $this->warn("⚠️ Could not read entrance from CVSecurity");
            }
        }
    }

    protected function testUpdateOrc()
    {
        $this->info('Testing Orc update with CVSecurity integration...');

        // Find an Orc with a CVSecurity code
        $orc = Orc::whereNotNull('code')->first();

        if (!$orc) {
            $this->error('❌ No Orc with CVSecurity code found. Please create one first.');
            return;
        }

        $this->info("Updating Orc: {$orc->number} (Code: {$orc->code})");

        $oldNumber = $orc->number;
        $newNumber = $oldNumber . '-UPDATED';

        // Update the Orc
        $orc->update([
            'number' => $newNumber,
        ]);

        $this->info("Updated Orc number from '{$oldNumber}' to '{$newNumber}'");

        // Wait a moment for the observer to process
        sleep(2);

        // Test reading from CVSecurity to verify update
        if ($orc->code) {
            $cvData = $this->cvSecurityService->getEntranceByCode($orc->code);
            if ($cvData) {
                $this->info("✅ Successfully read updated entrance from CVSecurity");
                $this->line("Name: " . ($cvData->area->name ?? 'N/A'));
                $this->line("Remark: " . ($cvData->area->remark ?? 'N/A'));
            } else {
                $this->warn("⚠️ Could not read entrance from CVSecurity");
            }
        }
    }

    protected function testDeleteOrc()
    {
        $this->info('Testing Orc deletion with CVSecurity integration...');

        // Find an Orc with a CVSecurity code that starts with TEST
        $orc = Orc::whereNotNull('code')
            ->where('number', 'like', 'TEST-%')
            ->first();

        if (!$orc) {
            $this->error('❌ No test Orc with CVSecurity code found. Please create one first.');
            return;
        }

        $this->info("Deleting Orc: {$orc->number} (Code: {$orc->code})");

        $code = $orc->code;

        // Delete the Orc
        $orc->delete();

        $this->info("Deleted Orc from local database");

        // Wait a moment for the observer to process
        sleep(2);

        // Test reading from CVSecurity to verify deletion
        $cvData = $this->cvSecurityService->getEntranceByCode($code);
        if (!$cvData) {
            $this->info("✅ Entrance successfully deleted from CVSecurity");
        } else {
            $this->warn("⚠️ Entrance still exists in CVSecurity (deletion might have failed)");
        }
    }
}
