<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetProductsWithLeftDaysRequest extends FormRequest
{
    const PARAMETER_ORSHIN_SUUGCH_TOOT_ID = 'orshin_suugch_toot_id';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_ORSHIN_SUUGCH_TOOT_ID => 'required|exists:orshin_suugch_toots,id',
        ];
    }
}
