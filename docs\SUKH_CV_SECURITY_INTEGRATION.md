# Sukhs CVSecurity Integration Documentation

## Overview

This document describes the integration between the Sukhs model and the CVSecurity service. The integration automatically synchronizes CRUD operations on Sukhs with the external CVSecurity system.

## Features

-   ✅ **Automatic Synchronization**: All CRUD operations on Sukhs are automatically synced with CVSecurity
-   ✅ **Error Handling**: Graceful handling of CVSecurity service unavailability
-   ✅ **Logging**: Comprehensive logging of all sync operations
-   ✅ **Code Storage**: CVSecurity response codes are stored in the `code` field
-   ✅ **Admin Panel Integration**: Code field is visible in Filament admin panel
-   ✅ **Testing**: Comprehensive test suite included

## Architecture

### Components

1. **SukhObserver** (`app/Observers/SukhObserver.php`)

    - Listens to Sukh model events (created, updated, deleted)
    - Triggers appropriate CVSecurity sync operations

2. **SukhSyncService** (`app/Services/SukhSyncService.php`)

    - Handles the actual synchronization with CVSecurity
    - Manages error handling and logging
    - Extracts codes from CVSecurity responses

3. **CvSecurityServiceExt** (`app/Services/CvSecurityService/CvSecurityServiceExt.php`)

    - Existing service for CVSecurity API communication
    - Provides CRUD operations for Sukhs in CVSecurity

4. **Database Migration** (`database/migrations/2025_05_27_062042_add_code_to_sukhs_table.php`)
    - Adds the `code` field to the sukhs table

## Database Schema Changes

### New Field: `code`

-   **Type**: `string`, nullable
-   **Purpose**: Stores the CVSecurity response code
-   **Location**: Added after `bag_id` field in sukhs table

```sql
ALTER TABLE sukhs ADD COLUMN code VARCHAR(255) NULL AFTER bag_id;
```

## Configuration

### Environment Variables

```env
CV_SECURITY_EXT_HOST=127.0.0.1
CV_SECURITY_EXT_PORT=8001
CV_SECURITY_API_KEY=1234567890abcdef
```

### Service Registration

The services are registered in `app/Providers/AppServiceProvider.php`:

```php
// Register CvSecurityServiceExt
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityServiceExt::class);

// Register SukhSyncService
$this->app->singleton(\App\Services\SukhSyncService::class);
```

### Observer Registration

The observer is registered in `app/Providers/AppServiceProvider.php`:

```php
// Register model observers
Sukh::observe(SukhObserver::class);
```

## Usage

### Automatic Synchronization

The integration works automatically. When you perform any CRUD operation on a Sukh:

```php
// Create - automatically syncs with CVSecurity
$sukh = Sukh::create([
    'name' => 'Test СӨХ',
    'registration_number' => 'T123456',
    'phone' => '99887766',
    'email' => '<EMAIL>',
    'aimag_id' => 1,
    'soum_id' => 1,
    'bag_id' => 1,
]);

// The code field will be automatically populated
echo $sukh->code; // e.g., "SUKH_0001"

// Update - automatically syncs with CVSecurity
$sukh->update(['name' => 'Updated СӨХ']);

// Delete - automatically syncs with CVSecurity
$sukh->delete();
```

### Manual Testing

Use the provided test command:

```bash
# Check CVSecurity service status
php artisan test:sukh-cv-security status

# Test creation
php artisan test:sukh-cv-security create

# Test update
php artisan test:sukh-cv-security update

# Test deletion
php artisan test:sukh-cv-security delete
```

## Error Handling

### Service Unavailable

When CVSecurity service is unavailable:

-   The `code` field is set to `null`
-   A warning is logged
-   The Sukh operation continues normally

### API Errors

When CVSecurity API returns errors:

-   The `code` field is set to `null`
-   An error is logged with response details
-   The Sukh operation continues normally

### Network Errors

When network issues occur:

-   The `code` field is set to `null`
-   An exception is logged with full stack trace
-   The Sukh operation continues normally

## Logging

All sync operations are logged with appropriate levels:

-   **INFO**: Successful operations
-   **WARNING**: Service unavailable
-   **ERROR**: API errors and exceptions

Log entries include:

-   Sukh ID and name
-   CVSecurity code (when available)
-   Response details (for errors)
-   Full stack traces (for exceptions)

## Testing

### Running Tests

```bash
# Run all integration tests
php artisan test tests/Feature/SukhCvSecurityIntegrationTest.php

# Run specific test
php artisan test tests/Feature/SukhCvSecurityIntegrationTest.php --filter="it_automatically_syncs_sukh_creation"
```

### Test Coverage

The test suite covers:

-   ✅ CVSecurity service status checking
-   ✅ Automatic sync on creation
-   ✅ Automatic sync on update
-   ✅ Automatic sync on deletion
-   ✅ Graceful handling of service unavailability
-   ✅ Model field validation
-   ✅ Response code extraction
-   ✅ Error handling

## Admin Panel Integration

The Filament admin panel has been updated to include the `code` field:

### Form Field

-   **Type**: TextInput (disabled)
-   **Label**: "CV Security код"
-   **Helper Text**: "Энэ талбар автоматаар CV Security системээс ирнэ"

### Table Column

-   **Type**: Badge column
-   **Colors**: Gray for empty, Success for assigned
-   **Format**: Shows "Байхгүй" when empty, actual code when assigned

## Troubleshooting

### Common Issues

1. **Code field not populated**

    - Check CVSecurity service status: `php artisan test:sukh-cv-security status`
    - Verify environment variables are set correctly
    - Check logs for error messages

2. **Observer not triggering**

    - Ensure observer is registered in AppServiceProvider
    - Clear application cache: `php artisan cache:clear`
    - Restart application server

3. **API authentication errors**
    - Verify `CV_SECURITY_API_KEY` is correct
    - Check CVSecurity service logs

### Debug Commands

```bash
# Check service status
php artisan test:sukh-cv-security status

# View recent logs
Get-Content storage/logs/laravel.log -Tail 50

# Clear cache
php artisan cache:clear
php artisan config:clear
```

## Best Practices

1. **Always check service availability** before making critical operations
2. **Monitor logs** for sync failures
3. **Handle null codes gracefully** in your application logic
4. **Test thoroughly** after any CVSecurity service updates
5. **Keep API keys secure** and rotate them regularly

## Future Enhancements

Potential improvements for the integration:

1. **Retry Mechanism**: Implement automatic retries for failed sync operations
2. **Queue Integration**: Use Laravel queues for async processing
3. **Bulk Operations**: Optimize for bulk Sukh operations
4. **Webhook Support**: Listen for CVSecurity webhooks for bidirectional sync
5. **Conflict Resolution**: Handle conflicts when data differs between systems
