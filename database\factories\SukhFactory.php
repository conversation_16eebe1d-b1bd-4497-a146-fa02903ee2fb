<?php

namespace Database\Factories;

use App\Models\Sukh;
use Illuminate\Database\Eloquent\Factories\Factory;

class SukhFactory extends Factory
{
    protected $model = Sukh::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'registration_number' => $this->faker->unique()->regexify('[A-Z]{2}[0-9]{5}'),
            'phone' => $this->faker->unique()->phoneNumber(),
            'email' => $this->faker->unique()->safeEmail(),
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ];
    }
}
