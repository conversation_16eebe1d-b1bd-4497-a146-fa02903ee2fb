<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginOsRequest extends FormRequest
{
    const PARAMETER_PHONE    = 'phone';
    const PARAMETER_OTP      = 'otp';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phone'   => 'required',
            'otp'     => 'required',
        ];
    }
}
