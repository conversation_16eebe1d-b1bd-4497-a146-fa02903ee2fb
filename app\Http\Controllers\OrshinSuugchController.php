<?php

namespace App\Http\Controllers;

use App\Enums\ProductEnum;
use App\Enums\CQPEnum;
use App\Models\Constant\ConstData;
use App\Models\OrshinSuugch;
use App\Models\OrshinSuugchToot;
use App\Models\DeviceDtl;
use App\Http\Requests\CheckOsRequest;
use App\Http\Requests\LoginOsRequest;
use App\Http\Requests\OpenDoorRequest;
use App\Http\Requests\GenerateAccessCodeRequest;
use App\Http\Requests\GetOsTootErkhsRequest;
use App\Http\Requests\SaveDeviceCodeRequest;
use App\Http\Resources\Token as TokenResource;
use App\Http\Resources\Me as MeResource;
use App\Http\Resources\OrshinSuugchTootWithProducts as OrshinSuugchTootWithProductsResource;
use App\Http\Resources\CheckDeviceCode as CheckDeviceCodeResource;
use App\Http\Resources\SaveDeviceCode as SaveDeviceCodeResource;
use App\Http\Resources\Erkh as ErkhResource;
use App\Http\Tools\DateTool;
use App\Services\OrcService;
use App\Services\OrshinSuugchService;
use App\Services\ErkhService;
use App\Services\PackageService;
use App\Services\SmsTokenService;
use App\Services\ToolService;
use App\Services\LogOpenDoorService;
use App\Services\ChirpStackService;
use App\Services\DeviceService;
use App\Exceptions\SystemException;

class OrshinSuugchController extends Controller
{
    public function getAuthOrshinSuugch()
    {
        $user         = auth()->user();
        $oshService   = resolve(OrshinSuugchService::class);
        $orshinSuugch = $oshService->getOrshinSuugchByPhone($user->phone);
        if (!isset($orshinSuugch))
            throw new SystemException(ConstData::AUTH_EXCEPTION, 4);
        return $orshinSuugch;
    }

    /**
     * Алхам 1. Оршин суугч системд нэвтрэх.
     *
     * Оршин суугч системд нэвтрэх үед бүртгэлтэй гар утасны дугаар оруулах ба уг дугаар руу нэг удаагийн дөрвөн орон бүхий тоо явуулна.
     * Оршин суугчын мэдээлэлийг тухайн CӨХ нь урьдчилан оруулж өгсөн байх ба бүтргэл нь хараахан идэвхжээгүй бол идэхжүүлж өгнө.
     */
    public function checkOs(CheckOsRequest $request)
    {
        $appEnv       = config('services.main.app_env');
        $phone        = $request->input(CheckOsRequest::PARAMETER_PHONE);
        $orshinSuugch = OrshinSuugch::where(OrshinSuugch::PHONE, $phone)->first();
        if (!isset($orshinSuugch))
            throw new SystemException(ConstData::AUTH_EXCEPTION, 1);

        if ($appEnv == ConstData::PRODUCTION) {
            $service = resolve(SmsTokenService::class);
            $service->sendCodeSms($phone);
        }

        return ConstData::successMSG();
    }

    /**
     * Алхам 2. Оршин суугч системд нэвтрэх.
     *
     * Бүртгэлтэй гар утасны дугаар дээр ирсэн нэг удаагийн нууц үг нь таарч байвал бүртгэлийг идэвхжүүлнэ.
     */
    public function loginOs(LoginOsRequest $request)
    {
        $appEnv       = config('services.main.app_env');
        $phone        = $request->input(LoginOsRequest::PARAMETER_PHONE);
        $otp          = $request->input(LoginOsRequest::PARAMETER_OTP);
        $orshinSuugch = OrshinSuugch::with(OrshinSuugch::RELATION_ORSHIN_SUUGCH_TOOTS)
            ->where(OrshinSuugch::PHONE, $phone)
            ->first();
        if (!isset($orshinSuugch))
            throw new SystemException(ConstData::AUTH_EXCEPTION, 1);

        $erkhService    = resolve(ErkhService::class);
        $packageService = resolve(PackageService::class);

        foreach ($orshinSuugch->orshin_suugch_toots as $key => $toot) {
            // $orc = resolve(OrcService::class)->getOrc($toot->korpus_id, $toot->number);
            $hasErkhByBairOrc = $erkhService->hasErkhByBairOrc($toot->orshin_suugch_id, $toot->korpus_id, $toot->number);
            if ($hasErkhByBairOrc)
                continue;
            $package = $packageService->getPackageForNewOs($toot->korpus_id);
            if (!isset($package))
                continue;
            $erkhService->setFreeErkh($toot->orshin_suugch_id, $toot->korpus_id, $toot->number, $package->id);
        }

        if ($appEnv == ConstData::PRODUCTION) {
            $service = resolve(SmsTokenService::class);
            $service->checkSmsTokenAfterSend($phone, $otp);
        }

        return new TokenResource($orshinSuugch->createToken($orshinSuugch->id));
    }

    /**
     * Оршин суугчийн өөрийн мэдэээлэл.
     *
     * Системд нэвтэрсэн оршин суугчийн мэдээлэлийг өөрт нь харуулах зориулалттай.
     */
    public function osme()
    {
        $orshinSuugch                   = $this->getAuthOrshinSuugch();
        $erkhService                    = resolve(ErkhService::class);
        $hasErkh                        = $erkhService->hasAnyErkh($orshinSuugch->id);
        $orshinSuugch->has_used_package = intval($hasErkh);
        return new MeResource($orshinSuugch);
    }

    /**
     * Нэвтрэх хүсэлт. FOR GATE
     *
     * Gate эрхтэй бол боломжтой
     * Оршин суугч өөрийн оршин суугаа байрны гадаа хаалгыг онгойлгох зориулалттай.
     */
    public function openGate(OpenDoorRequest $request)
    {
        $korpusId     = $request->input(OpenDoorRequest::PARAMETER_KORPUS_ID);
        $number       = $request->input(OpenDoorRequest::PARAMETER_NUMBER);
        $orshinSuugch = $this->getAuthOrshinSuugch();
        $erkhService  = resolve(ErkhService::class);
        $erkh         = $erkhService->getCurrentGateErkh($orshinSuugch->id, $korpusId, $number);
        if (!isset($erkh))
            throw new SystemException(ConstData::ERKH_EXCEPTION, 2);

        // $deviceService = resolve(DeviceService::class);
        // $devEui        = $deviceService->getDevEuiOs($orshinSuugch->id, $korpusId, $orcId);

        // if (!isset($devEui))
        //     throw new SystemException(ConstData::SYSTEM_EXCEPTION, 15);

        // $logOpenDoorService = resolve(LogOpenDoorService::class);
        // $logOpenDoor        = $logOpenDoorService->createLogOpenDoor($orshinSuugch->id, $devEui, ProductEnum::GATE);

        // $data         = base64_encode($logOpenDoor->log_no);
        // $chirpService = resolve(ChirpStackService::class);
        // $chirpService->postDeviceQueue($devEui, $data, CQPEnum::GATE);

        // return ConstData::successMSG();
    }

    /**
     * Нэвтрэх хүсэлты.
     *
     * Pass эрхтэй үед боломжтой
     * Оршин суугч өөрийн оршин суугаа байрны гадаа хаалгыг онгойлгох зориулалттай 4н орон бүхий кодыг зохиолгож авах.
     */
    public function generateAccessCode(GenerateAccessCodeRequest $request)
    {
        $korpusId     = $request->input(GenerateAccessCodeRequest::PARAMETER_KORPUS_ID);
        $number       = $request->input(GenerateAccessCodeRequest::PARAMETER_NUMBER);
        $orshinSuugch = $this->getAuthOrshinSuugch();

        $orshinSuugchToot = OrshinSuugchToot::where(OrshinSuugchToot::ORSHIN_SUUGCH_ID, $orshinSuugch->id)
            ->where(OrshinSuugchToot::KORPUS_ID, $korpusId)
            ->where(OrshinSuugchToot::NUMBER, $number)
            ->first();

        if (!isset($orshinSuugchToot))
            throw new SystemException(ConstData::ORSHIN_SUUGCH_EXCEPTION, 1);

        if (isset($orshinSuugchToot->ac_generated_date)) {
            $diffHour = DateTool::getLeftHoursFromEndDateTime($orshinSuugchToot->ac_generated_date);
            if (24 > $diffHour)
                throw new SystemException(ConstData::ORSHIN_SUUGCH_EXCEPTION, 2);
        }

        $orshinSuugchToot = OrshinSuugchToot::where(OrshinSuugchToot::ORSHIN_SUUGCH_ID, $orshinSuugch->id)
            ->where(OrshinSuugchToot::KORPUS_ID, $korpusId)
            ->where(OrshinSuugchToot::NUMBER, $number)
            ->first();

        $orc = resolve(OrcService::class)->getOrc($korpusId, $number);

        $deviceService = resolve(DeviceService::class);
        $devEui        = $deviceService->getDevEuiOs($orshinSuugch->id, $korpusId, $orc->id);

        // remove access code from device
        $data         = base64_encode('0;'.$orshinSuugchToot->number.';'.$orshinSuugchToot->access_code);
        $chirpService = resolve(ChirpStackService::class);
        $chirpService->postDeviceQueue($devEui, $data, CQPEnum::PASS);

        $toolService = resolve(ToolService::class);
        $accessCode  = $toolService->random_digits(4);
        // add access code to device
        $data         = base64_encode('1;'.$orshinSuugchToot->number.';'.$accessCode);
        $chirpService = resolve(ChirpStackService::class);
        $chirpService->postDeviceQueue($devEui, $data, CQPEnum::PASS);

        $orshinSuugchToot->update([
            OrshinSuugchToot::ACCESS_CODE => $accessCode,
            OrshinSuugchToot::AC_GENERATED_DATE => DateTool::nowDateTime()
        ]);
        return ConstData::successMSG();
    }

    /**
     * Оршин суугчийн тоотын мэдээлэлийг жагсаалтаар авах.
     *
     * Тухайн оршин суугчийн хамаарал бүхий тоотуудын мэдээлэлийг авах зориулалттай.
     */
    public function getOsToots()
    {
        $orshinSuugch = $this->getAuthOrshinSuugch();
        $osToots      = OrshinSuugchToot::where(OrshinSuugchToot::ORSHIN_SUUGCH_ID, $orshinSuugch->id)->get();
        $nowDate      = DateTool::nowDate();
        $erkhService  = resolve(ErkhService::class);
        foreach ($osToots as $key => $osToot) {
            $orc    = resolve(OrcService::class)->getOrc($osToot->korpus_id, $osToot->number);
            $erkhs  = $erkhService->getCurrentErkhs($orshinSuugch->id, $osToot->korpus_id, $osToot->number);
            $keyLD  = 0;
            $passLD = 0;
            $gateLD = 0;
            $productsWithLeftDays = collect();
            foreach ($erkhs as $erkh) {
                if ($nowDate < $erkh->begin_date || $nowDate > $erkh->end_date)
                    continue;
                foreach ($erkh->products as $productName) {
                    $leftDay = DateTool::getLeftDaysFromEndDates($erkh->end_date);
                    switch ($productName) {
                        case ProductEnum::KEY:
                            $keyLD += $leftDay;
                            break;
                        case ProductEnum::PASS:
                            $passLD += $leftDay;
                            break;
                        case ProductEnum::GATE:
                            $gateLD += $leftDay;
                                break;
                    }
                }
            }
            $value = 0;
            if ($keyLD > 0) {
                $deviceDtl = DeviceDtl::where(DeviceDtl::KORPUS_ID, $osToot->korpus_id)->where(DeviceDtl::ORC_ID, $orc->id)->first();
                if (isset($deviceDtl))
                    $value = $deviceDtl->dev_eui;
                $productsWithLeftDays->push((object)["product" => ProductEnum::KEY, "left_day" => $keyLD, "value" => $value]);
            }
            if ($passLD > 0) {
                $orshinSuugchToot = OrshinSuugchToot::where(OrshinSuugchToot::ORSHIN_SUUGCH_ID, $orshinSuugch->id)
                            ->where(OrshinSuugchToot::KORPUS_ID, $osToot->korpus_id)
                            ->where(OrshinSuugchToot::NUMBER, $osToot->number)
                            ->first();
                if (isset($orshinSuugchToot))
                    $value = $orshinSuugchToot->access_code;
                $productsWithLeftDays->push((object)["product" => ProductEnum::PASS, "left_day" => $passLD, "value" => $value]);
            }
            if ($gateLD > 0) {
                $productsWithLeftDays->push((object)["product" => ProductEnum::GATE, "left_day" => $gateLD, "value" => $value]);
            }
            $osToot->products = $productsWithLeftDays;
        }
        return OrshinSuugchTootWithProductsResource::collection($osToots);
    }

    /**
     * Оршин суугчийн тоотын бүтээгдэхүүний эрхүүдийг жагсаалтаар авах.
     *
     * Оршин суугчийн тоотын бүтээгдэхүүнтэй холбоотой бүх эрхүүдийн мэдээлэлийг авах зориулалттай.
     */
    public function getOsTootErkhs(GetOsTootErkhsRequest $request)
    {
        $this->getAuthOrshinSuugch();
        $id                 = $request->input(GetOsTootErkhsRequest::PARAMETER_ID);
        $product            = $request->input(GetOsTootErkhsRequest::PARAMETER_PRODUCT);
        $orshinSuugchToot   = OrshinSuugchToot::find($id);

        $erkhService  = resolve(ErkhService::class);
        $erkhs        = $erkhService->getCurrentProductErkhs($orshinSuugchToot->orshin_suugch_id, $orshinSuugchToot->korpus_id, $orshinSuugchToot->number, $product);
        return ErkhResource::collection($erkhs);
    }

    /**
     * Гар утасны дахин давтагдашгүй код нь таарч байгаа эсэх.
     *
     * Тухайн оршин суугчийн гар утасны дахин давтагдашгүй код нь бүртгэлтэй код мөн эсэхийг шалгах зориулалттай.
     */
    public function checkDeviceCode(String $deviceCode)
    {
        $orshinSuugch = $this->getAuthOrshinSuugch();
        return new CheckDeviceCodeResource((object)['value' => $orshinSuugch->device_code == $deviceCode]);
    }

    /**
     * Гар утасны дахин давтагдашгүй кодыг авах.
     *
     * Тухайн оршин суугчийн гар утасны дахин давтагдашгүй кодыг авах зориулалттай.
     */
    public function saveDeviceCode(SaveDeviceCodeRequest $request)
    {
        $deviceCode   = $request->input(SaveDeviceCodeRequest::PARAMETER_DEVICE_CODE);
        $orshinSuugch = $this->getAuthOrshinSuugch();
        $orshinSuugch->device_code = $deviceCode;
        $orshinSuugch->save();
        return new SaveDeviceCodeResource($orshinSuugch);
    }
}
