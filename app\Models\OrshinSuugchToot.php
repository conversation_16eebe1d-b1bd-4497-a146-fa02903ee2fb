<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * App\Models\OrshinSuugchToot
 *
 * @mixin IdeHelperOrshinSuugchToot
 * @property int $id
 * @property int $orshin_suugch_id
 * @property string $number
 * @property string|null $access_code
 * @property string|null $ac_generated_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $state_bank_code
 * @property int $korpus_id
 * @property-read \App\Models\Korpus|null $korpus
 * @property-read \App\Models\OrshinSuugch $orshin_suugch
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereAcGeneratedDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereAccessCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereKorpusId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereOrshinSuugchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereStateBankCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchToot whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class OrshinSuugchToot extends Model
{
    use HasFactory;

    const ID                          = 'id';
    const ORSHIN_SUUGCH_ID            = 'orshin_suugch_id';
    const KORPUS_ID                   = 'korpus_id';
    const NUMBER                      = 'number';
    const ACCESS_CODE                 = 'access_code';
    const AC_GENERATED_DATE           = 'ac_generated_date';
    const STATE_BANK_CODE             = 'state_bank_code';

    const RELATION_ORSHIN_SUUGCH      = 'orshin_suugch';
    const RELATION_KORPUS             = 'korpus';

    const BAIR_ID                     = 'bair_id';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::ORSHIN_SUUGCH_ID,
        self::KORPUS_ID,
        self::NUMBER,
        self::STATE_BANK_CODE
    ];

    public function orshin_suugch(): BelongsTo
    {
        return $this->belongsTo(OrshinSuugch::class);
    }

    public function korpus(): BelongsTo
    {
        return $this->belongsTo(Korpus::class);
    }
}
