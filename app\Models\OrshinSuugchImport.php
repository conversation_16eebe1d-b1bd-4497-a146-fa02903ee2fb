<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\OrshinSuugchImport
 *
 * @mixin IdeHelperOrshinSuugchImport
 * @property int $id
 * @property string $status
 * @property string $message
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport query()
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder|OrshinSuugchImport whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class OrshinSuugchImport extends Model
{
    use HasFactory;

    const ID                     = 'id';
    const STATUS                 = 'status';
    const MESSAGE                = 'message';

    const ATTACHMENT             = 'attachment';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $fillable = [
        self::ID,
        self::STATUS,
        self::MESSAGE,
    ];
}
