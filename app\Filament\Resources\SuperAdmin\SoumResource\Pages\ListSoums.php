<?php

namespace App\Filament\Resources\SuperAdmin\SoumResource\Pages;

use App\Filament\Resources\SuperAdmin\SoumResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListSoums extends ListRecords
{
    protected static string $resource = SoumResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх')->icon('heroicon-s-plus'),
        ];
    }
}
