<?php

namespace App\Filament\Resources\SuperAdmin\UserResource\Pages;

use App\Models\Constant\ConstData;
use App\Models\Role;
use App\Filament\Resources\SuperAdmin\UserResource;
use Filament\Resources\Pages\CreateRecord;

class CreateUser extends CreateRecord
{
    protected static string $resource = UserResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['password'] = 'password';
        $data['is_active'] = true;
        return $data;
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        $adminRole = Role::where(Role::NAME, ConstData::ROLE_ADMIN)->first();
        $record->roles()->attach($adminRole->id);
    }
}
