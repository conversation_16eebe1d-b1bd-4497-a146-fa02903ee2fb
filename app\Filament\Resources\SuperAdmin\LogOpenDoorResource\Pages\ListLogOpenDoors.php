<?php

namespace App\Filament\Resources\SuperAdmin\LogOpenDoorResource\Pages;

use App\Filament\Resources\SuperAdmin\LogOpenDoorResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListLogOpenDoors extends ListRecords
{
    protected static string $resource = LogOpenDoorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
