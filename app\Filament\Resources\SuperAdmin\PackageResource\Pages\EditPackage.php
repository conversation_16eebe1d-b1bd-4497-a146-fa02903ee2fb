<?php

namespace App\Filament\Resources\SuperAdmin\PackageResource\Pages;

use App\Services\PackageService;
use App\Filament\Resources\SuperAdmin\PackageResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditPackage extends EditRecord
{
    protected static string $resource = PackageResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        $record = $this->record;
        $data   = $this->data;
        if (!$record->is_new_os_erkh)
            return;
        $service = resolve(PackageService::class);
        $service->setNewOsErkhForPackage($data['sukhs'], $record->id);
    }
}
