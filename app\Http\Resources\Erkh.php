<?php

namespace App\Http\Resources;

use App\Http\Tools\DateTool;
use Illuminate\Http\Resources\Json\JsonResource;

class Erkh extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                => $this->id,
            'begin_date'        => $this->begin_date,
            'end_date'          => $this->end_date,
            'left_day'          => DateTool::getDaysFromBetweenDates($this->begin_date, $this->end_date),
            'invoice'           => $this->invoice ? new SimpleInvoice($this->invoice) : null,
        ];
    }
}
