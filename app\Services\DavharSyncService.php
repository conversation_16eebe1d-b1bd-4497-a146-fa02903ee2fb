<?php

namespace App\Services;

use App\Models\Davhar;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Support\Facades\Log;

class DavharSyncService
{
    protected CvSecurityServiceExt $cvSecurityService;

    public function __construct(CvSecurityServiceExt $cvSecurityService)
    {
        $this->cvSecurityService = $cvSecurityService;
    }

    /**
     * Sync Davhar creation with CVSecurity service
     *
     * @param Davhar $davhar
     * @return void
     */
    public function syncCreate(Davhar $davhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Davhar creation', [
                    'davhar_id' => $davhar->id,
                    'davhar_number' => $davhar->number
                ]);
                $this->updateDavharCode($davhar, null);
                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareDavharData($davhar);

            // Call CVSecurity create EleLevel endpoint
            $response = $this->cvSecurityService->createEleLevel($data);

            $code = $this->extractCodeFromResponse($response);

            if ($code) {
                $this->updateDavharCode($davhar, $code);
                Log::info('Davhar successfully synced with CVSecurity on creation', [
                    'davhar_id' => $davhar->id,
                    'cv_code' => $code
                ]);
            } else {
                $this->updateDavharCode($davhar, null);
                Log::error('CVSecurity create operation failed for Davhar', [
                    'davhar_id' => $davhar->id,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            $this->updateDavharCode($davhar, null);
            Log::error('Exception during Davhar CVSecurity sync on creation', [
                'davhar_id' => $davhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync Davhar update with CVSecurity service
     *
     * @param Davhar $davhar
     * @return void
     */
    public function syncUpdate(Davhar $davhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Davhar update', [
                    'davhar_id' => $davhar->id,
                    'davhar_number' => $davhar->number
                ]);
                return;
            }

            // If no code exists, create instead of update
            if (!$davhar->code) {
                Log::info('No CVSecurity code found for Davhar, creating instead of updating', [
                    'davhar_id' => $davhar->id
                ]);
                $this->syncCreate($davhar);
                return;
            }

            // Prepare data for CVSecurity
            $data = $this->prepareDavharData($davhar);

            // Call CVSecurity update EleLevel endpoint
            $response = $this->cvSecurityService->updateEleLevel($davhar->code, $data);

            if ($response) {
                Log::info('Davhar successfully synced with CVSecurity on update', [
                    'davhar_id' => $davhar->id,
                    'cv_code' => $davhar->code
                ]);
            } else {
                Log::error('CVSecurity update operation failed for Davhar', [
                    'davhar_id' => $davhar->id,
                    'cv_code' => $davhar->code,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Davhar CVSecurity sync on update', [
                'davhar_id' => $davhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync Davhar deletion with CVSecurity service
     *
     * @param Davhar $davhar
     * @return void
     */
    public function syncDelete(Davhar $davhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                Log::warning('CVSecurity service is not available during Davhar deletion', [
                    'davhar_id' => $davhar->id,
                    'davhar_number' => $davhar->number
                ]);
                return;
            }

            // If no code exists, nothing to delete from CVSecurity
            if (!$davhar->code) {
                Log::info('No CVSecurity code found for Davhar, skipping deletion', [
                    'davhar_id' => $davhar->id
                ]);
                return;
            }

            // Call CVSecurity delete EleLevel endpoint
            $response = $this->cvSecurityService->deleteEleLevel($davhar->code);

            if ($response) {
                Log::info('Davhar successfully deleted from CVSecurity', [
                    'davhar_id' => $davhar->id,
                    'cv_code' => $davhar->code
                ]);
            } else {
                Log::error('CVSecurity delete operation failed for Davhar', [
                    'davhar_id' => $davhar->id,
                    'cv_code' => $davhar->code,
                    'response' => $response
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Davhar CVSecurity sync on deletion', [
                'davhar_id' => $davhar->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Prepare Davhar data for CVSecurity API
     *
     * @param Davhar $davhar
     * @return array
     */
    private function prepareDavharData(Davhar $davhar): array
    {
        // Load the Orc relationship to get parent code
        $davhar->load('orc');
        
        $parentCode = $davhar->orc->code ?? null;

        return [
            'name' => "Floor {$davhar->number}",
            'remark' => "Floor {$davhar->number} in Entrance {$davhar->orc->number}",
            'parent_code' => $parentCode,
        ];
    }

    /**
     * Extract code from CVSecurity response
     *
     * @param mixed $response
     * @return string|null
     */
    private function extractCodeFromResponse($response): ?string
    {
        if (is_array($response) && isset($response['code'])) {
            return $response['code'];
        }

        if (is_object($response) && property_exists($response, 'code')) {
            return $response->code;
        }

        // Try to decode JSON response
        if (is_string($response)) {
            $decoded = json_decode($response, true);
            if (is_array($decoded) && isset($decoded['code'])) {
                return $decoded['code'];
            }
        }

        return null;
    }

    /**
     * Update Davhar code field
     *
     * @param Davhar $davhar
     * @param string|null $code
     * @return void
     */
    private function updateDavharCode(Davhar $davhar, ?string $code): void
    {
        // Update without triggering observers to avoid infinite loops
        $davhar->updateQuietly([Davhar::CODE => $code]);
    }

    /**
     * Sync Davhar read with CVSecurity service (optional)
     *
     * @param Davhar $davhar
     * @return void
     */
    public function syncRead(Davhar $davhar): void
    {
        try {
            // Check if CVSecurity service is available
            if (!$this->cvSecurityService->isServiceAvailable()) {
                return;
            }

            // If no code exists, nothing to read from CVSecurity
            if (!$davhar->code) {
                return;
            }

            // Call CVSecurity get EleLevel endpoint
            $response = $this->cvSecurityService->getEleLevelByCode($davhar->code);

            if ($response) {
                Log::debug('Davhar successfully read from CVSecurity', [
                    'davhar_id' => $davhar->id,
                    'cv_code' => $davhar->code
                ]);
            }

        } catch (\Exception $e) {
            Log::error('Exception during Davhar CVSecurity sync on read', [
                'davhar_id' => $davhar->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
