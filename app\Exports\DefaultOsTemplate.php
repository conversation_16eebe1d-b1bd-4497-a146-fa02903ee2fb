<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;

class DefaultOsTemplate implements FromCollection
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $headerRow = [
            'Овог',
            'Нэр',
            'Утас',
            'Цахим шуудангийн хаяг',
            'Байр',
            'Блок',
            'Тоот',
            'Төрийн банкны код',
        ];
        $bodyRow = [
            'Тест-Бат',
            'Тест-Болд',
            '12345678',
            '<EMAIL>',
            '101',
            '1',
            '1',
            '1234567891000000',
        ];
        return collect([
            $headerRow,
            $bodyRow
        ]);
    }
}
