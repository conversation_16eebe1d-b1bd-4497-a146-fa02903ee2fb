<?php

namespace App\Filament\Resources\Admin\UilchilgeeResource\Pages;

use App\Filament\Resources\Admin\UilchilgeeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListUilchilgees extends ListRecords
{
    protected static string $resource = UilchilgeeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
