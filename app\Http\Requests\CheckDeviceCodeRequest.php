<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CheckDeviceCodeRequest extends FormRequest
{
    const PARAMETER_DEVICE_CODE           = 'device_code';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_DEVICE_CODE      => 'required|string|max:255',
        ];
    }
}
