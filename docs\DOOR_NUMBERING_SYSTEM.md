# Door Numbering System Documentation

## Overview

This document describes the comprehensive door numbering system used in the Smart Door IoT application. The system supports multiple numbering schemes to accommodate different building layouts and organizational requirements.

## Hierarchical Structure

The door numbering system follows this hierarchical structure:

```
Su<PERSON> (Residents' Committee)
└── Bair (Building) - examples: 57, 105
    └── Korpus (Block) - examples: A, B, 1
        └── Orc (Entrance) - examples: 1, 2
            └── Davhar (Floor) - examples: 4, 12, 16
                └── Toot (Door) - examples: 1, 101, 1106
```

## Numbering Types

### Type 1: Korpus-wise Consecutive Numbering (Variable Range)

This is the primary numbering system where each Korpus has a configurable range of doors numbered consecutively. The starting and ending numbers can vary based on the building's requirements.

#### Basic Structure

-   **Total doors per Korpus**: Variable (e.g., 192, 150, 240)
-   **Numbering range**: Variable (e.g., 1-192, 101-250, 1-150)
-   **Distribution**: Doors are distributed across Orcs and Davhars within each Korpus
-   **Flexibility**: Starting number doesn't have to be 1, ending number doesn't have to be 192

#### Example Configuration

**Korpus with 2 Orcs and 16 Davhars per Orc:**

```
Korpus A (192 doors total)
├── Orc 1 (doors 1-96)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── ...
│   └── Davhar 16: doors 91-96
└── Orc 2 (doors 97-192)
    ├── Davhar 1: doors 97-102
    ├── Davhar 2: doors 103-108
    ├── Davhar 3: doors 109-114
    ├── ...
    └── Davhar 16: doors 187-192
```

#### Calculation Formula

For a Korpus with `N` Orcs, `F` Davhars per Orc, starting from `START` and ending at `END`:

```
Total doors = END - START + 1
Doors per Orc = Total doors / N
Doors per Davhar = (Total doors / N) / F

For Orc i (0-indexed):
  Start door = START + (i * doors_per_orc)
  End door = START + ((i + 1) * doors_per_orc) - 1

For Davhar j within Orc i (0-indexed):
  Start door = START + (i * doors_per_orc) + (j * doors_per_davhar)
  End door = START + (i * doors_per_orc) + ((j + 1) * doors_per_davhar) - 1
```

#### Detailed Examples

**Example 1: 2 Orcs, 16 Davhars per Orc (6 doors per Davhar)**

```
Korpus A:
├── Orc 1 (96 doors: 1-96)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── Davhar 4: doors 19-24
│   ├── Davhar 5: doors 25-30
│   ├── Davhar 6: doors 31-36
│   ├── Davhar 7: doors 37-42
│   ├── Davhar 8: doors 43-48
│   ├── Davhar 9: doors 49-54
│   ├── Davhar 10: doors 55-60
│   ├── Davhar 11: doors 61-66
│   ├── Davhar 12: doors 67-72
│   ├── Davhar 13: doors 73-78
│   ├── Davhar 14: doors 79-84
│   ├── Davhar 15: doors 85-90
│   └── Davhar 16: doors 91-96
└── Orc 2 (96 doors: 97-192)
    ├── Davhar 1: doors 97-102
    ├── Davhar 2: doors 103-108
    ├── Davhar 3: doors 109-114
    ├── Davhar 4: doors 115-120
    ├── Davhar 5: doors 121-126
    ├── Davhar 6: doors 127-132
    ├── Davhar 7: doors 133-138
    ├── Davhar 8: doors 139-144
    ├── Davhar 9: doors 145-150
    ├── Davhar 10: doors 151-156
    ├── Davhar 11: doors 157-162
    ├── Davhar 12: doors 163-168
    ├── Davhar 13: doors 169-174
    ├── Davhar 14: doors 175-180
    ├── Davhar 15: doors 181-186
    └── Davhar 16: doors 187-192
```

**Example 2: 4 Orcs, 8 Davhars per Orc (6 doors per Davhar)**

```
Korpus B:
├── Orc 1 (48 doors: 1-48)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── Davhar 4: doors 19-24
│   ├── Davhar 5: doors 25-30
│   ├── Davhar 6: doors 31-36
│   ├── Davhar 7: doors 37-42
│   └── Davhar 8: doors 43-48
├── Orc 2 (48 doors: 49-96)
│   ├── Davhar 1: doors 49-54
│   ├── Davhar 2: doors 55-60
│   ├── Davhar 3: doors 61-66
│   ├── Davhar 4: doors 67-72
│   ├── Davhar 5: doors 73-78
│   ├── Davhar 6: doors 79-84
│   ├── Davhar 7: doors 85-90
│   └── Davhar 8: doors 91-96
├── Orc 3 (48 doors: 97-144)
│   ├── Davhar 1: doors 97-102
│   ├── Davhar 2: doors 103-108
│   ├── Davhar 3: doors 109-114
│   ├── Davhar 4: doors 115-120
│   ├── Davhar 5: doors 121-126
│   ├── Davhar 6: doors 127-132
│   ├── Davhar 7: doors 133-138
│   └── Davhar 8: doors 139-144
└── Orc 4 (48 doors: 145-192)
    ├── Davhar 1: doors 145-150
    ├── Davhar 2: doors 151-156
    ├── Davhar 3: doors 157-162
    ├── Davhar 4: doors 163-168
    ├── Davhar 5: doors 169-174
    ├── Davhar 6: doors 175-180
    ├── Davhar 7: doors 181-186
    └── Davhar 8: doors 187-192
```

### Type 2: Orc-wise Consecutive Numbering

This numbering system restarts the door numbering from 1 for each Orc within a Korpus. Each Orc has its own independent numbering sequence.

#### Basic Structure

-   **Numbering per Orc**: Each Orc starts from 1
-   **Independence**: Door numbers are independent between Orcs
-   **Distribution**: Doors are distributed across Davhars within each Orc
-   **Flexibility**: Each Orc can have different total door counts

#### Example Configuration

**Korpus with 3 Orcs and varying Davhars per Orc:**

```
Korpus A
├── Orc 1 (48 doors: 1-48)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── Davhar 4: doors 19-24
│   ├── Davhar 5: doors 25-30
│   ├── Davhar 6: doors 31-36
│   ├── Davhar 7: doors 37-42
│   └── Davhar 8: doors 43-48
├── Orc 2 (36 doors: 1-36)
│   ├── Davhar 1: doors 1-6
│   ├── Davhar 2: doors 7-12
│   ├── Davhar 3: doors 13-18
│   ├── Davhar 4: doors 19-24
│   ├── Davhar 5: doors 25-30
│   └── Davhar 6: doors 31-36
└── Orc 3 (60 doors: 1-60)
    ├── Davhar 1: doors 1-6
    ├── Davhar 2: doors 7-12
    ├── Davhar 3: doors 13-18
    ├── Davhar 4: doors 19-24
    ├── Davhar 5: doors 25-30
    ├── Davhar 6: doors 31-36
    ├── Davhar 7: doors 37-42
    ├── Davhar 8: doors 43-48
    ├── Davhar 9: doors 49-54
    └── Davhar 10: doors 55-60
```

#### Calculation Formula

For an Orc with `F` Davhars and `TOTAL_DOORS` doors:

```
Doors per Davhar = TOTAL_DOORS / F

For Davhar j within an Orc (0-indexed):
  Start door = (j * doors_per_davhar) + 1
  End door = (j + 1) * doors_per_davhar
```

#### Detailed Example

**Example: 2 Orcs with different configurations**

```
Korpus B:
├── Orc 1 (24 doors: 1-24)
│   ├── Davhar 1: doors 1-8
│   ├── Davhar 2: doors 9-16
│   └── Davhar 3: doors 17-24
└── Orc 2 (30 doors: 1-30)
    ├── Davhar 1: doors 1-10
    ├── Davhar 2: doors 11-20
    └── Davhar 3: doors 21-30
```

#### Key Differences from Type 1

-   **Independent numbering**: Each Orc starts from 1
-   **No Korpus-wide range**: No overall door limit for the Korpus
-   **Flexible Orc sizes**: Each Orc can have different total door counts
-   **Simpler calculation**: No need to consider other Orcs when calculating door ranges

### Type 3: Floor-wise Consecutive Numbering

This numbering system incorporates the floor (Davhar) number into the door number. Each door number consists of the floor number followed by the door sequence within that floor.

#### Basic Structure

-   **Floor-based numbering**: Door numbers include floor number as prefix
-   **Format**: `{floor_number}{door_sequence}` with variable digit lengths
-   **Flexible digits**: Can use 2-digit (11, 12), 3-digit (101, 102), or 4-digit (1001, 1002) formats
-   **Consistency**: Same pattern applies to all Orcs within the Korpus
-   **Sequential doors**: Doors are numbered sequentially within each floor

#### Example Configuration

**Korpus with 2 Orcs, 16 Davhars per Orc, 6 doors per Davhar:**

```
Korpus A
├── Orc 1
│   ├── Davhar 1 (Floor 1): doors 101-106
│   ├── Davhar 2 (Floor 2): doors 201-206
│   ├── Davhar 3 (Floor 3): doors 301-306
│   ├── Davhar 4 (Floor 4): doors 401-406
│   ├── Davhar 5 (Floor 5): doors 501-506
│   ├── Davhar 6 (Floor 6): doors 601-606
│   ├── Davhar 7 (Floor 7): doors 701-706
│   ├── Davhar 8 (Floor 8): doors 801-806
│   ├── Davhar 9 (Floor 9): doors 901-906
│   ├── Davhar 10 (Floor 10): doors 1001-1006
│   ├── Davhar 11 (Floor 11): doors 1101-1106
│   ├── Davhar 12 (Floor 12): doors 1201-1206
│   ├── Davhar 13 (Floor 13): doors 1301-1306
│   ├── Davhar 14 (Floor 14): doors 1401-1406
│   ├── Davhar 15 (Floor 15): doors 1501-1506
│   └── Davhar 16 (Floor 16): doors 1601-1606
└── Orc 2
    ├── Davhar 1 (Floor 1): doors 101-106
    ├── Davhar 2 (Floor 2): doors 201-206
    ├── Davhar 3 (Floor 3): doors 301-306
    ├── Davhar 4 (Floor 4): doors 401-406
    ├── Davhar 5 (Floor 5): doors 501-506
    ├── Davhar 6 (Floor 6): doors 601-606
    ├── Davhar 7 (Floor 7): doors 701-706
    ├── Davhar 8 (Floor 8): doors 801-806
    ├── Davhar 9 (Floor 9): doors 901-906
    ├── Davhar 10 (Floor 10): doors 1001-1006
    ├── Davhar 11 (Floor 11): doors 1101-1106
    ├── Davhar 12 (Floor 12): doors 1201-1206
    ├── Davhar 13 (Floor 13): doors 1301-1306
    ├── Davhar 14 (Floor 14): doors 1401-1406
    ├── Davhar 15 (Floor 15): doors 1501-1506
    └── Davhar 16 (Floor 16): doors 1601-1606
```

#### Calculation Formula

For a Davhar with floor number `FLOOR_NUM`, `DOORS_PER_FLOOR` doors, and `DIGIT_MULTIPLIER`:

```
For door i within a Davhar (1-indexed):
  Door number = (FLOOR_NUM * DIGIT_MULTIPLIER) + i

Where:
  FLOOR_NUM = Davhar sequence number (1, 2, 3, ...)
  i = Door sequence within the floor (1, 2, 3, ..., DOORS_PER_FLOOR)
  DIGIT_MULTIPLIER = 10 (2-digit), 100 (3-digit), 1000 (4-digit), etc.

Examples:
  2-digit format: Floor 1, Door 1 = (1 * 10) + 1 = 11
  3-digit format: Floor 1, Door 1 = (1 * 100) + 1 = 101
  4-digit format: Floor 1, Door 1 = (1 * 1000) + 1 = 1001
```

#### Detailed Examples

**Example 1: 2-digit format (small building)**

```
Korpus B (2-digit format):
├── Orc 1
│   ├── Davhar 1 (Floor 1): doors 11-14 (4 doors)
│   ├── Davhar 2 (Floor 2): doors 21-24 (4 doors)
│   ├── Davhar 3 (Floor 3): doors 31-34 (4 doors)
│   └── Davhar 4 (Floor 4): doors 41-44 (4 doors)
└── Orc 2
    ├── Davhar 1 (Floor 1): doors 11-18 (8 doors)
    ├── Davhar 2 (Floor 2): doors 21-28 (8 doors)
    └── Davhar 3 (Floor 3): doors 31-38 (8 doors)
```

**Example 2: 3-digit format (medium building)**

```
Korpus C (3-digit format):
├── Orc 1
│   ├── Davhar 1 (Floor 1): doors 101-104 (4 doors)
│   ├── Davhar 2 (Floor 2): doors 201-204 (4 doors)
│   ├── Davhar 3 (Floor 3): doors 301-304 (4 doors)
│   └── Davhar 4 (Floor 4): doors 401-404 (4 doors)
└── Orc 2
    ├── Davhar 1 (Floor 1): doors 101-108 (8 doors)
    ├── Davhar 2 (Floor 2): doors 201-208 (8 doors)
    └── Davhar 3 (Floor 3): doors 301-308 (8 doors)
```

**Example 3: 4-digit format (high-rise building)**

```
Korpus D (4-digit format):
├── Orc 1
│   ├── Davhar 1 (Floor 1): doors 1001-1002
│   ├── Davhar 2 (Floor 2): doors 2001-2002
│   ├── ...
│   ├── Davhar 25 (Floor 25): doors 25001-25002
│   └── Davhar 26 (Floor 26): doors 26001-26002
└── Orc 2
    ├── Davhar 1 (Floor 1): doors 1001-1002
    ├── Davhar 2 (Floor 2): doors 2001-2002
    ├── ...
    ├── Davhar 25 (Floor 25): doors 25001-25002
    └── Davhar 26 (Floor 26): doors 26001-26002
```

#### Key Features of Type 3

-   **Floor identification**: Door number immediately indicates the floor
-   **Consistent pattern**: Same numbering pattern across all Orcs
-   **Scalable**: Works for buildings with many floors
-   **Intuitive**: Easy to understand and navigate
-   **Duplicate across Orcs**: Same door numbers can exist in different Orcs

## Database Implementation

### Model Fields

#### Korpus Model

```php
class Korpus extends Model
{
    // Door range for the entire Korpus (Type 1 only)
    protected $fillable = [
        'begin_toot_number', // Variable start for Type 1 (e.g., 1, 101)
        'end_toot_number',   // Variable end for Type 1 (e.g., 192, 250)
        'numbering_type',    // 1 = Korpus-wise, 2 = Orc-wise, 3 = Floor-wise
        'digit_multiplier',  // Type 3: 10 (2-digit), 100 (3-digit), 1000 (4-digit)
        // ... other fields
    ];
}
```

#### Orc Model

```php
class Orc extends Model
{
    // Door range for this specific Orc
    protected $fillable = [
        'begin_toot_number', // Type 1: calculated range, Type 2: always 1, Type 3: N/A
        'end_toot_number',   // Type 1: calculated range, Type 2: total doors, Type 3: N/A
        // ... other fields
    ];
}
```

#### Davhar Model

```php
class Davhar extends Model
{
    // Door range for this specific Davhar within the Orc
    protected $fillable = [
        'begin_toot_number', // Type 1&2: calculated range, Type 3: floor-based (e.g., 11, 101, 1001)
        'end_toot_number',   // Type 1&2: calculated range, Type 3: floor-based (e.g., 16, 106, 1006)
        'floor_number',      // Type 3: floor identifier (1, 2, 3, ...)
        // ... other fields
    ];
}
```

#### Toot Model

```php
class Toot extends Model
{
    // Individual door number
    protected $fillable = [
        'number',    // Type 1&2: sequential, Type 3: variable digits (11, 12 or 101, 102 or 1001, 1002)
        'korpus_id', // Reference to Korpus
        'davhar_id', // Reference to Davhar (optional)
        // ... other fields
    ];
}
```

## Configuration Examples

### Small Building Configuration

-   **1 Korpus, 1 Orc, 4 Davhars**
-   **48 doors per Davhar**

```
Korpus A:
└── Orc 1 (192 doors: 1-192)
    ├── Davhar 1: doors 1-48
    ├── Davhar 2: doors 49-96
    ├── Davhar 3: doors 97-144
    └── Davhar 4: doors 145-192
```

### Medium Building Configuration

-   **1 Korpus, 3 Orcs, 8 Davhars per Orc**
-   **8 doors per Davhar**

```
Korpus A:
├── Orc 1 (64 doors: 1-64)
│   ├── Davhar 1: doors 1-8
│   ├── Davhar 2: doors 9-16
│   ├── ...
│   └── Davhar 8: doors 57-64
├── Orc 2 (64 doors: 65-128)
│   ├── Davhar 1: doors 65-72
│   ├── ...
│   └── Davhar 8: doors 121-128
└── Orc 3 (64 doors: 129-192)
    ├── Davhar 1: doors 129-136
    ├── ...
    └── Davhar 8: doors 185-192
```

## Implementation Guidelines

### Creating Door Ranges

#### Type 1: Korpus-wise Consecutive Numbering

1. **Korpus Level**: Set configurable `begin_toot_number` and `end_toot_number` (e.g., 1-192, 101-250)
2. **Orc Level**: Calculate based on number of Orcs and total Korpus range
3. **Davhar Level**: Calculate based on number of Davhars in the Orc
4. **Toot Level**: Assign individual numbers within the Davhar range

#### Type 2: Orc-wise Consecutive Numbering

1. **Korpus Level**: No door range needed (numbering_type = 2)
2. **Orc Level**: Set `begin_toot_number = 1` and `end_toot_number = total_doors_in_orc`
3. **Davhar Level**: Calculate based on number of Davhars and total Orc doors
4. **Toot Level**: Assign individual numbers within the Davhar range

#### Type 3: Floor-wise Consecutive Numbering

1. **Korpus Level**: Set `numbering_type = 3` and `digit_multiplier` (10, 100, 1000, etc.)
2. **Orc Level**: No door range needed (doors calculated per floor)
3. **Davhar Level**: Set `floor_number` and calculate floor-based door range
4. **Toot Level**: Assign floor-based numbers using formula: `(floor_number * digit_multiplier) + sequence`

### Validation Rules

#### Type 1 Validation

1. Door numbers must be unique within a Korpus
2. Orc ranges must not overlap within a Korpus
3. Davhar ranges must not overlap within an Orc
4. All door numbers must fall within the Korpus range
5. The sum of all Orc ranges must equal the total Korpus range
6. The sum of all Davhar ranges within an Orc must equal the Orc's range

#### Type 2 Validation

1. Door numbers must be unique within each Orc (can duplicate across Orcs)
2. Davhar ranges must not overlap within an Orc
3. All door numbers must fall within the 1 to total_doors_in_orc range
4. The sum of all Davhar ranges within an Orc must equal the Orc's total doors

#### Type 3 Validation

1. Door numbers must be unique within each Orc (can duplicate across Orcs)
2. Floor numbers must be unique within each Orc
3. Door numbers must follow the format: `(floor_number * digit_multiplier) + sequence`
4. Door sequence within a floor must start from 1 and be consecutive
5. Floor numbers should typically start from 1 and be consecutive
6. Digit multiplier must be consistent across the entire Korpus (10, 100, 1000, etc.)
7. Door sequence must not exceed the digit capacity (e.g., max 9 doors for 2-digit, 99 for 3-digit)

## Future Extensions

The system is designed to support additional numbering types:

-   **Type 4**: Building-wide consecutive numbering
-   **Type 5**: Custom numbering schemes
-   **Type 6**: Mixed numbering schemes (different types per Orc)

Each type can be implemented by extending the current model structure and adding appropriate validation and calculation logic.

## Summary

The door numbering system now supports three main types:

1. **Type 1 (Korpus-wise Consecutive)**: Variable range numbering across the entire Korpus with configurable start and end numbers
2. **Type 2 (Orc-wise Consecutive)**: Independent numbering for each Orc, starting from 1
3. **Type 3 (Floor-wise Consecutive)**: Floor-based numbering where door numbers include floor number as prefix with variable digit formats (e.g., 11-12, 101-102, 1001-1002)

All three types provide flexibility for different building layouts and organizational requirements while maintaining data integrity through proper validation rules. Type 3 is particularly useful for buildings where floor identification is important for navigation and management, with the flexibility to choose appropriate digit formats based on building size and door count requirements.
