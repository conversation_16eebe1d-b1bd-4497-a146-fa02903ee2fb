<?php

namespace App\Http\Controllers;

use App\Models\OrshinSuugchToot;
use App\Http\Requests\GetSukhBillsRequest;
use App\Http\Requests\CreateInvoiceRequest;
use App\Http\Requests\CreateTransactionRequest;
use App\Http\Resources\BpayInvoice as BpayInvoiceResource;
use App\Http\Resources\BpayBill as BpayBillResource;
use App\Http\Resources\BpayTransaction as BpayTransactionResource;
use App\Http\Resources\BpayCheckInvoice as BpayCheckInvoiceResource;
use App\Services\BPayService;
use App\Services\OrshinSuugchService;

class BPayContoller extends Controller
{
    public function getAUOrshinSuugch()
    {
        $user = auth()->user();
        return resolve(OrshinSuugchService::class)->getOrshinSuugchByPhone($user->phone);
    }

    public function getSumBillAmount(GetSukhBillsRequest $request)
    {
        $osTootId     = $request->input(GetSukhBillsRequest::PARAMETER_ORSHIN_SUUGCH_TOOT_ID);
        $orshinSuugch = $this->getAUOrshinSuugch();
        $osToot       = OrshinSuugchToot::find($osTootId);
        $invoices     = resolve(BPayService::class)->getInvoicesByCID($orshinSuugch, $osToot->state_bank_code);
        $sukhInvoice  = collect($invoices)->first(function ($invoice) {
                return $invoice->name === 'CӨX';
            });
        $totalPaidAmount = collect($sukhInvoice->bills)->sum('paidAmount');
        return ['total_amount' => (int) $totalPaidAmount];
    }

    public function getSukhBills(GetSukhBillsRequest $request)
    {
        $osTootId     = $request->input(GetSukhBillsRequest::PARAMETER_ORSHIN_SUUGCH_TOOT_ID);
        $orshinSuugch = $this->getAUOrshinSuugch();
        $osToot       = OrshinSuugchToot::find($osTootId);
        $invoices     = resolve(BPayService::class)->getInvoicesByCID($orshinSuugch, $osToot->state_bank_code);
        $sukhInvoice  = collect($invoices)->first(function ($invoice) {
                return $invoice->name === 'CӨX';
            });
        $bills = collect($sukhInvoice->bills)->sortBy([
            ['year', 'asc'],
            ['month', 'asc'],
        ])->values()->all();
        return BpayBillResource::collection($bills);
    }

    public function createInvoice(CreateInvoiceRequest $request)
    {
        $invoiceIds   = $request->input(CreateInvoiceRequest::PARAMETER_BILL_IDS);
        $orshinSuugch = $this->getAUOrshinSuugch();
        $bpayinvoice  = resolve(BPayService::class)->createInvoice($orshinSuugch, $invoiceIds);
        return new BpayInvoiceResource($bpayinvoice);
    }

    public function createTransaction(CreateTransactionRequest $request)
    {
        $invoiceId    = $request->input(CreateTransactionRequest::PARAMETER_INVOICE_ID);
        $isOrg        = $request->input(CreateTransactionRequest::PARAMETER_IS_ORG);
        $vatInfo      = $request->input(CreateTransactionRequest::PARAMETER_VAT_INFO);
        $orshinSuugch = $this->getAUOrshinSuugch();
        $bpayTrans    = resolve(BPayService::class)->createTransaction($orshinSuugch, $isOrg, $vatInfo, $invoiceId);
        return new BpayTransactionResource($bpayTrans);
    }

    public function checkInvoice($invoiceId)
    {
        $orshinSuugch = $this->getAUOrshinSuugch();
        $result       = resolve(BPayService::class)->checkInvoice($orshinSuugch, $invoiceId);
        return new BpayCheckInvoiceResource($result);
    }
}
