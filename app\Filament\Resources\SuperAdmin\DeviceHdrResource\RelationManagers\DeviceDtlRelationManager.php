<?php

namespace App\Filament\Resources\SuperAdmin\DeviceHdrResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class DeviceDtlRelationManager extends RelationManager
{
    protected static string $relationship = 'device_dtls';

    public function form(Form $form): Form
    {
        return $form
            ->schema([

            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр'),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок'),
                Tables\Columns\TextColumn::make('orc.number')->label('Орц'),
                Tables\Columns\TextColumn::make('dev_eui')->label('DevEui'),
                Tables\Columns\TextColumn::make('join_eui')->label('JoinEui'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
            ])
            ->actions([
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
            ]);
    }
}
