<?php

namespace Database\Factories;

use App\Models\Bair;
use App\Models\Sukh;
use Illuminate\Database\Eloquent\Factories\Factory;

class BairFactory extends Factory
{
    protected $model = Bair::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->numberBetween(1, 999),
            'sukh_id' => Sukh::factory(),
            'aimag_id' => 1,
            'soum_id' => 1,
            'bag_id' => 1,
        ];
    }
}
