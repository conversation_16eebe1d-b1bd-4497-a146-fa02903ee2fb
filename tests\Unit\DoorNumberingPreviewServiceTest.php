<?php

namespace Tests\Unit;

use App\Services\DoorNumberingPreviewService;
use PHPUnit\Framework\TestCase;

class DoorNumberingPreviewServiceTest extends TestCase
{
    protected DoorNumberingPreviewService $service;

    protected function setUp(): void
    {
        parent::setUp();
        $this->service = new DoorNumberingPreviewService();
    }

    public function test_empty_preview_when_no_numbering_type()
    {
        $config = [];
        $result = $this->service->generatePreview($config);
        
        $this->assertArrayHasKey('empty', $result);
        $this->assertTrue($result['empty']);
    }

    public function test_error_when_invalid_floors_count()
    {
        $config = [
            'numbering_type' => 1,
            'floors_count' => 0,
            'doors_per_floor' => 6,
            'orcs' => [['number' => 1]]
        ];
        
        $result = $this->service->generatePreview($config);
        
        $this->assertArrayHasKey('error', $result);
    }

    public function test_type_1_preview_generation()
    {
        $config = [
            'numbering_type' => 1,
            'digit_multiplier' => 100,
            'floors_count' => 2,
            'doors_per_floor' => 6,
            'orcs' => [
                ['number' => 1],
                ['number' => 2]
            ]
        ];
        
        $result = $this->service->generatePreview($config);
        
        $this->assertEquals(1, $result['type']);
        $this->assertEquals('Төрөл 1: Блокийн дугаарлалт', $result['type_name']);
        $this->assertEquals(24, $result['total_doors']); // 2 orcs * 2 floors * 6 doors
        $this->assertCount(2, $result['orcs']);
        
        // Check first orc
        $firstOrc = $result['orcs'][0];
        $this->assertEquals(1, $firstOrc['number']);
        $this->assertEquals(2, $firstOrc['floors_count']);
        $this->assertCount(2, $firstOrc['floors']);
        
        // Check door ranges for Type 1 (consecutive across all orcs)
        $this->assertEquals('1-6', $firstOrc['floors'][0]['door_range']);
        $this->assertEquals('7-12', $firstOrc['floors'][1]['door_range']);
        
        $secondOrc = $result['orcs'][1];
        $this->assertEquals('13-18', $secondOrc['floors'][0]['door_range']);
        $this->assertEquals('19-24', $secondOrc['floors'][1]['door_range']);
    }

    public function test_type_2_preview_generation()
    {
        $config = [
            'numbering_type' => 2,
            'digit_multiplier' => 100,
            'floors_count' => 2,
            'doors_per_floor' => 6,
            'orcs' => [
                ['number' => 1],
                ['number' => 2]
            ]
        ];
        
        $result = $this->service->generatePreview($config);
        
        $this->assertEquals(2, $result['type']);
        $this->assertEquals('Төрөл 2: Орцны дугаарлалт', $result['type_name']);
        $this->assertEquals(24, $result['total_doors']);
        
        // Check that each orc starts from 1 (Type 2 behavior)
        $firstOrc = $result['orcs'][0];
        $this->assertEquals('1-6', $firstOrc['floors'][0]['door_range']);
        $this->assertEquals('7-12', $firstOrc['floors'][1]['door_range']);
        
        $secondOrc = $result['orcs'][1];
        $this->assertEquals('1-6', $secondOrc['floors'][0]['door_range']); // Resets to 1
        $this->assertEquals('7-12', $secondOrc['floors'][1]['door_range']);
    }

    public function test_type_3_preview_generation()
    {
        $config = [
            'numbering_type' => 3,
            'digit_multiplier' => 100,
            'floors_count' => 2,
            'doors_per_floor' => 6,
            'orcs' => [
                ['number' => 1]
            ]
        ];
        
        $result = $this->service->generatePreview($config);
        
        $this->assertEquals(3, $result['type']);
        $this->assertEquals('Төрөл 3: Давхрын дугаарлалт', $result['type_name']);
        $this->assertEquals(12, $result['total_doors']);
        
        // Check Type 3 door numbering (floor number * digit_multiplier + door sequence)
        $orc = $result['orcs'][0];
        $this->assertEquals('101-106', $orc['floors'][0]['door_range']); // Floor 1: 1*100 + 1-6
        $this->assertEquals('201-206', $orc['floors'][1]['door_range']); // Floor 2: 2*100 + 1-6
    }

    public function test_type_3_with_different_digit_multipliers()
    {
        // Test 2-digit format
        $config = [
            'numbering_type' => 3,
            'digit_multiplier' => 10,
            'floors_count' => 1,
            'doors_per_floor' => 6,
            'orcs' => [['number' => 1]]
        ];
        
        $result = $this->service->generatePreview($config);
        $this->assertEquals('11-16', $result['orcs'][0]['floors'][0]['door_range']);
        
        // Test 4-digit format
        $config['digit_multiplier'] = 1000;
        $result = $this->service->generatePreview($config);
        $this->assertEquals('1001-1006', $result['orcs'][0]['floors'][0]['door_range']);
    }

    public function test_floors_count_override()
    {
        $config = [
            'numbering_type' => 1,
            'floors_count' => 2,
            'doors_per_floor' => 6,
            'orcs' => [
                ['number' => 1, 'floors_count_override' => 3], // Override to 3 floors
                ['number' => 2] // Use default 2 floors
            ]
        ];
        
        $result = $this->service->generatePreview($config);
        
        $this->assertEquals(30, $result['total_doors']); // (3*6) + (2*6) = 30
        $this->assertEquals(3, $result['orcs'][0]['floors_count']);
        $this->assertEquals(2, $result['orcs'][1]['floors_count']);
    }
}
