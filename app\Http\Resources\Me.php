<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Me extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        $members           = !$this->members ? [] : $this->members;
        $orshinSuugchToots = !$this->orshin_suugch_toots ? [] : $this->orshin_suugch_toots;
        return [
            'id'                        => $this->id,
            'last_name'                 => $this->last_name,
            'name'                      => $this->name,
            'phone'                     => intval($this->phone),
            'email'                     => $this->email,
            'is_admin'                  => $this->is_admin,
            'has_used_package'          => $this->has_used_package,
            'members_count'             => count($members),
            'orshin_suugch_toots_count' => count($orshinSuugchToots),
            'members'                   => Member::collection($members),
            'orshin_suugch_toots'       => OrshinSuugchToot::collection($orshinSuugchToots),
        ];
    }
}
