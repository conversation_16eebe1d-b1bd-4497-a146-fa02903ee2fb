<?php

/**
 * OrshinSuugch CVSecurity Integration Demo
 * 
 * This script demonstrates how the OrshinSuugch CVSecurity integration works.
 * Run this in Laravel Tinker: php artisan tinker
 * Then copy and paste the code sections below.
 */

// ===== DEMO SCRIPT =====
// Run in Laravel Tinker: php artisan tinker

// 1. Check if CVSecurity service is available
$cvService = app(\App\Services\CvSecurityService\CvSecurityService::class);
$isAvailable = $cvService->isServiceAvailable();
echo "CVSecurity service available: " . ($isAvailable ? 'Yes' : 'No') . "\n";

// 2. Create a new OrshinSuugch (this will automatically sync with CVSecurity)
$orshinSuugch = \App\Models\OrshinSuugch::create([
    'name' => 'Demo Person',
    'last_name' => 'Demo Last Name',
    'phone' => '99123456',
    'email' => '<EMAIL>',
    'is_admin' => false,
]);

echo "Created OrshinSuugch with ID: {$orshinSuugch->id}\n";
echo "CVSecurity code: {$orshinSuugch->code}\n";

// 3. Update the OrshinSuugch (this will automatically sync with CVSecurity)
$orshinSuugch->update(['name' => 'Updated Demo Person']);
echo "Updated OrshinSuugch name to: {$orshinSuugch->name}\n";

// 4. Check the sync service directly
$syncService = app(\App\Services\OrshinSuugchSyncService::class);

// Manual sync operations (normally handled automatically by observer)
// $syncService->syncCreate($orshinSuugch);
// $syncService->syncUpdate($orshinSuugch);

// 5. Test CVSecurity service methods directly
if ($isAvailable) {
    // Get next person pin (mock implementation)
    $nextPin = $cvService->getNextPersonPin();
    echo "Next available pin: {$nextPin}\n";
    
    // Create person data
    $personData = [
        'pin' => $nextPin,
        'name' => 'Direct API Test',
        'lastName' => 'Test Last Name',
        'mobilePhone' => '99999999',
        'email' => '<EMAIL>'
    ];
    
    // Create person via API
    $response = $cvService->createOrUpdatePerson($personData);
    if ($response) {
        echo "Successfully created person via API\n";
    }
    
    // Get person via API
    $person = $cvService->getPerson($nextPin);
    if ($person) {
        echo "Successfully retrieved person via API\n";
    }
    
    // Delete person via API
    $deleteResponse = $cvService->deletePerson($nextPin);
    if ($deleteResponse) {
        echo "Successfully deleted person via API\n";
    }
}

// 6. Clean up - delete the demo OrshinSuugch (this will automatically sync deletion with CVSecurity)
$orshinSuugch->delete();
echo "Deleted demo OrshinSuugch\n";

// ===== VERIFICATION COMMANDS =====

// Check all OrshinSuugch records with codes
// \App\Models\OrshinSuugch::whereNotNull('code')->get(['id', 'name', 'phone', 'code']);

// Check logs for sync operations
// tail -f storage/logs/laravel.log | grep "OrshinSuugch"

// Check CVSecurity service status
// app(\App\Services\CvSecurityService\CvSecurityService::class)->getServiceStatus();

// ===== TROUBLESHOOTING =====

// If sync is not working, check:
// 1. CVSecurity service configuration
// config('services.cv_security');

// 2. Observer registration
// Check app/Providers/AppServiceProvider.php for OrshinSuugch::observe(OrshinSuugchObserver::class);

// 3. Service registration
// Check app/Providers/AppServiceProvider.php for OrshinSuugchSyncService singleton registration

// 4. Database migration
// Check if 'code' field exists in orshin_suugches table
// \Schema::hasColumn('orshin_suugches', 'code');

// 5. Model configuration
// Check if 'code' is in fillable array
// (new \App\Models\OrshinSuugch())->getFillable();
