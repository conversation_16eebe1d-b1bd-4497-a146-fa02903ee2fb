@php
    // Fallback if previewData is not available
    if (!isset($previewData)) {
        $previewData = ['empty' => true];
    }
@endphp

<div class="filament-forms-field-wrapper">
    {{-- Component Label --}}
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-medium leading-4 text-gray-700 dark:text-gray-300">
            Хаалганы дугаарлалтын урьдчилан харах
        </h3>
    </div>
    <div class="space-y-4">
        @if(isset($previewData['empty']))
            <div class="text-sm text-gray-500 italic">
                Тохиргоо сонгосны дараа урьдчилан харах боломжтой болно
            </div>
        @elseif(isset($previewData['error']))
            <div class="text-sm text-red-600">
                {{ $previewData['error'] }}
            </div>
        @else
            {{-- Preview Header --}}
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center space-x-2 mb-2">
                    <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                    <h3 class="text-lg font-semibold text-blue-800">Урьдчилан харах</h3>
                </div>
                <p class="text-sm text-blue-700 mb-2">{{ $previewData['description'] }}</p>
                <div class="text-sm text-amber-800 bg-amber-100 border border-amber-300 rounded-lg px-3 py-2 mt-3">
                    <div class="flex items-center space-x-2">
                        <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.314 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                        </svg>
                        <strong>Урьдчилан харах - Өгөгдлийн санд хадгалагдахгүй</strong>
                    </div>
                    <p class="mt-1 text-sm">
                        Энэ нь зөвхөн урьдчилан харах зориулалттай. Давхар болон хаалганууд "Хадгалах" товчийг дарсны дараа өгөгдлийн санд үүсгэгдэнэ.
                    </p>
                </div>
            </div>

            {{-- Preview Content --}}
            <div class="space-y-4">
                @foreach($previewData['orcs'] as $orc)
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-800">
                                Орц {{ $orc['number'] }}
                            </h4>
                            <div class="text-sm text-gray-600">
                                {{ $orc['floors_count'] }} давхар
                                @if(isset($orc['total_doors']))
                                    • {{ $orc['total_doors'] }} хаалга
                                @endif
                            </div>
                        </div>

                        {{-- Floors Grid --}}
                        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            @foreach($orc['floors'] as $floor)
                                <div class="bg-white border border-gray-300 rounded-md p-3">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium text-gray-700">
                                            Давхар {{ $floor['number'] }}
                                        </span>
                                        <span class="text-xs text-gray-500">
                                            {{ $floor['door_count'] }} хаалга
                                        </span>
                                    </div>
                                    <div class="text-lg font-bold text-blue-600 bg-blue-50 rounded px-2 py-1 text-center">
                                        {{ $floor['door_range'] }}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>

            {{-- Summary --}}
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center space-x-2">
                    <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="font-semibold text-green-800">
                        Нийт: {{ $previewData['total_doors'] }} хаалга
                    </span>
                </div>
                @if($previewData['type'] == 3)
                    <div class="text-sm text-green-700 mt-1">
                        Цифрийн формат: {{ $previewData['digit_multiplier'] == 10 ? '2 цифр' : ($previewData['digit_multiplier'] == 100 ? '3 цифр' : '4 цифр') }}
                    </div>
                @endif
            </div>
        @endif
    </div>
</div>
