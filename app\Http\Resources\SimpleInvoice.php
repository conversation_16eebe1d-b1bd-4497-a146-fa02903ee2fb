<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class SimpleInvoice extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                    => $this->id,
            'invoice_description'   => $this->invoice_description,
            'amount'                => $this->amount,
            'status'                => $this->status,
            'package_id'            => $this->package_id,
            'package_name'          => $this->package_name,
        ];
    }
}
