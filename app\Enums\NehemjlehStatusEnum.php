<?php

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum NehemjlehStatusEnum:string implements HasLabel, HasColor
{
    case TULUGDUUGUI  = 'tulugduugui';
    case TULSUN       = 'tulsun';
    case DUTUU_TULSUN = 'dutuu_tulsun';
    case ILUU_TULSUN  = 'iluu_tulsun';

    public static function getValues(): array {
        return [
            'tulugduugui',
            'tulsun',
            'dutuu_tulsun',
            'iluu_tulsun'
        ];
    }

    public function getLabel(): string
    {
        return match ($this) {
            self::TULUGDUUGUI  => 'Төлөгдөөгүй',
            self::TULSUN       => 'Төлөгдсөн',
            self::DUTUU_TULSUN => 'Дүтүү төлөгдсөн',
            self::ILUU_TULSUN  => 'Илүү төлөгдсөн',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::TULUGDUUGUI  => 'gray',
            self::DUTUU_TULSUN => 'warning',
            self::TULSUN       => 'success',
            self::ILUU_TULSUN  => 'danger',
        };
    }
}
