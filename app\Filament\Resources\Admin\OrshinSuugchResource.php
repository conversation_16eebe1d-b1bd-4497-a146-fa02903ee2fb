<?php

namespace App\Filament\Resources\Admin;

use App\Models\OrshinSuugch;
use App\Services\UserInfoService;
use App\Filament\Resources\Admin\OrshinSuugchResource\Pages;
use App\Filament\Resources\Admin\OrshinSuugchResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class OrshinSuugchResource extends Resource
{
    protected static ?string $model = OrshinSuugch::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?string $navigationLabel = 'Оршин суугч';
    protected static ?string $modelLabel = 'оршин суугч';
    protected static ?string $pluralModelLabel = 'Оршин суугч';
    protected static ?int $navigationSort = 3;
    protected static ?string $slug = 'orshinsuugches';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make(OrshinSuugch::LAST_NAME)
                            ->label('Овог')
                            ->maxValue(50),

                        Forms\Components\TextInput::make(OrshinSuugch::NAME)
                            ->label('Нэр')
                            ->maxValue(50)
                            ->required(),

                        Forms\Components\TextInput::make(OrshinSuugch::PHONE)
                            ->label('Гар утас')
                            ->required()
                            ->maxValue(50),

                        Forms\Components\TextInput::make(OrshinSuugch::EMAIL)
                            ->label('Имэйл хаяг')
                            ->email()
                            ->unique(ignoreRecord: true),

                        Forms\Components\TextInput::make(OrshinSuugch::UNIQ_CODE)
                            ->label('BPAY код')
                            // ->disabled(fn (?OrshinSuugch $record) => $record != null)
                            ->hidden(fn (?OrshinSuugch $record) => $record === null),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?OrshinSuugch $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Created at')
                            ->content(fn (OrshinSuugch $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Last modified at')
                            ->content(fn (OrshinSuugch $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?OrshinSuugch $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make('last_name')->label('Овог')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('name')->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('phone')->label('Гар утас')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('email')->label('Имэйл хаяг')->sortable()->searchable(),
                Tables\Columns\BooleanColumn::make('is_admin')->label('Админ эсэх')->sortable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([

            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrshinSuugchTootsRelationManager::class
        ];
    }

    public static function getPages(): array
    {
        return [
            'index'  => Pages\ListOrshinSuugches::route('/'),
            'create' => Pages\CreateOrshinSuugch::route('/create'),
            'edit'   => Pages\EditOrshinSuugch::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh    = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas(OrshinSuugch::RELATION_SUKHS, function (Builder $query) use($sukh) {
            $query->where('sukh_id', $sukh->id);
        });
    }
}
