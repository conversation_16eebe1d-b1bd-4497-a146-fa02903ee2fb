<?php

namespace App\Filament\Resources\SuperAdmin\LogOpenDoorResource\Pages;

use App\Filament\Resources\SuperAdmin\LogOpenDoorResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditLogOpenDoor extends EditRecord
{
    protected static string $resource = LogOpenDoorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
