<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetCurrentProductErkhsRequest extends FormRequest
{
    const PARAMETER_ORSHIN_SUUGCH_TOOT_ID = 'orshin_suugch_toot_id';
    const PARAMETER_PRODUCT = 'product';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_ORSHIN_SUUGCH_TOOT_ID => 'required|exists:orshin_suugch_toots,id',
            self::PARAMETER_PRODUCT               => 'required|string',
        ];
    }
}
