<?php

namespace Tests\Unit;

use App\Models\Davhar;
use App\Models\Orc;
use App\Services\DavharSyncService;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Tests\TestCase;
use Mockery;

class DavharSyncServiceTest extends TestCase
{
    protected $davharSyncService;
    protected $cvSecurityServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the CVSecurity service
        $this->cvSecurityServiceMock = Mockery::mock(CvSecurityServiceExt::class);
        $this->davharSyncService = new DavharSyncService($this->cvSecurityServiceMock);
    }

    /** @test */
    public function it_syncs_davhar_creation_successfully()
    {
        // Arrange
        $orc = Mockery::mock(Orc::class);
        $orc->shouldReceive('getAttribute')->with('number')->andReturn('1');
        $orc->shouldReceive('getAttribute')->with('code')->andReturn('ENTR_000001');

        $davhar = Mockery::mock(Davhar::class);
        $davhar->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $davhar->shouldReceive('getAttribute')->with('number')->andReturn('4');
        $davhar->shouldReceive('getAttribute')->with('orc')->andReturn($orc);
        $davhar->shouldReceive('load')->with('orc')->andReturnSelf();
        $davhar->shouldReceive('updateQuietly')->with(['code' => 'ELEV_000001'])->once();

        // Mock CVSecurity service responses
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(true);

        $this->cvSecurityServiceMock
            ->shouldReceive('createEleLevel')
            ->once()
            ->with([
                'name' => 'Floor 4',
                'remark' => 'Floor 4 in Entrance 1',
                'parent_code' => 'ENTR_000001',
            ])
            ->andReturn(['code' => 'ELEV_000001']);

        // Act
        $this->davharSyncService->syncCreate($davhar);

        // Assert - Mockery will verify the expectations
        $this->assertTrue(true);
    }

    /** @test */
    public function it_handles_service_unavailable_during_creation()
    {
        // Arrange
        $davhar = Mockery::mock(Davhar::class);
        $davhar->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $davhar->shouldReceive('getAttribute')->with('number')->andReturn('4');
        $davhar->shouldReceive('updateQuietly')->with(['code' => null])->once();

        // Mock CVSecurity service as unavailable
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(false);

        // Act
        $this->davharSyncService->syncCreate($davhar);

        // Assert - Mockery will verify the expectations
        $this->assertTrue(true);
    }

    /** @test */
    public function it_syncs_davhar_update_successfully()
    {
        // Arrange
        $orc = Mockery::mock(Orc::class);
        $orc->shouldReceive('getAttribute')->with('number')->andReturn('1');
        $orc->shouldReceive('getAttribute')->with('code')->andReturn('ENTR_000001');

        $davhar = Mockery::mock(Davhar::class);
        $davhar->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $davhar->shouldReceive('getAttribute')->with('number')->andReturn('5');
        $davhar->shouldReceive('getAttribute')->with('code')->andReturn('ELEV_000001');
        $davhar->shouldReceive('getAttribute')->with('orc')->andReturn($orc);
        $davhar->shouldReceive('load')->with('orc')->andReturnSelf();

        // Mock CVSecurity service responses
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(true);

        $this->cvSecurityServiceMock
            ->shouldReceive('updateEleLevel')
            ->once()
            ->with('ELEV_000001', [
                'name' => 'Floor 5',
                'remark' => 'Floor 5 in Entrance 1',
                'parent_code' => 'ENTR_000001',
            ])
            ->andReturn(['code' => 'ELEV_000001']);

        // Act
        $this->davharSyncService->syncUpdate($davhar);

        // Assert - Mockery will verify the expectations
        $this->assertTrue(true);
    }

    /** @test */
    public function it_syncs_davhar_deletion_successfully()
    {
        // Arrange
        $davhar = Mockery::mock(Davhar::class);
        $davhar->shouldReceive('getAttribute')->with('id')->andReturn(1);
        $davhar->shouldReceive('getAttribute')->with('number')->andReturn('4');
        $davhar->shouldReceive('getAttribute')->with('code')->andReturn('ELEV_000001');

        // Mock CVSecurity service responses
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(true);

        $this->cvSecurityServiceMock
            ->shouldReceive('deleteEleLevel')
            ->once()
            ->with('ELEV_000001')
            ->andReturn(true);

        // Act
        $this->davharSyncService->syncDelete($davhar);

        // Assert - Mockery will verify the expectations
        $this->assertTrue(true);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
