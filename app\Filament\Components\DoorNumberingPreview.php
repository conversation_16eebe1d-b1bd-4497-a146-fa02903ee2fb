<?php

namespace App\Filament\Components;

use App\Services\DoorNumberingPreviewService;
use Filament\Forms\Components\View;

class DoorNumberingPreview extends View
{
    protected string $view = 'filament.components.door-numbering-preview';

    public static function make(string $name): static
    {
        $static = new static($name);
        $static->view('filament.components.door-numbering-preview');
        return $static;
    }

    /**
     * Get the view data for the component
     */
    public function getViewData(): array
    {
        return array_merge(parent::getViewData(), [
            'previewData' => $this->evaluate(fn() => $this->getPreviewData()),
        ]);
    }

    public function reactive(): static
    {
        $this->live();
        return $this;
    }

    /**
     * Make the component reactive for real-time updates
     */
    public function enableRealTimeUpdates(): static
    {
        // Enable live updates for real-time preview
        $this->live();
        return $this;
    }

    /**
     * Get the preview data based on current form state
     */
    public function getPreviewData(): array
    {
        try {
            $previewService = app(DoorNumberingPreviewService::class);

            // Get form state using the get callback
            $get = $this->getGetCallback();

            if (!$get) {
                return ['empty' => true];
            }

            $numberingType = $get('numbering_type');
            $digitMultiplier = $get('digit_multiplier') ?? 100;
            $floorsCount = $get('floors_count') ?? 1;
            $doorsPerFloor = $get('doors_per_floor') ?? 6;
            $orcs = $get('orcs') ?? [];

            // If no numbering type is selected, return empty preview
            if (!$numberingType) {
                return ['empty' => true];
            }

            // Convert Orcs data to the format expected by preview service
            $orcData = [];
            foreach ($orcs as $orc) {
                if (isset($orc['number'])) {
                    $orcData[] = [
                        'number' => $orc['number'],
                        'floors_count_override' => $orc['floors_count_override'] ?? null
                    ];
                }
            }

            // If no Orcs are defined, create sample data for preview
            if (empty($orcData)) {
                $orcData = [
                    ['number' => 1],
                    ['number' => 2]
                ];
            }

            return $previewService->generatePreview([
                'numbering_type' => $numberingType,
                'digit_multiplier' => $digitMultiplier,
                'floors_count' => $floorsCount,
                'doors_per_floor' => $doorsPerFloor,
                'orcs' => $orcData
            ]);
        } catch (\Exception $e) {
            return ['error' => 'Урьдчилан харахад алдаа гарлаа: ' . $e->getMessage()];
        }
    }


}
