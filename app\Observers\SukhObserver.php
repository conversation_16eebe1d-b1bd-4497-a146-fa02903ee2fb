<?php

namespace App\Observers;

use App\Models\Sukh;
use App\Services\SukhSyncService;
use Illuminate\Support\Facades\Log;

class SukhObserver
{
    protected SukhSyncService $sukhSyncService;

    public function __construct(SukhSyncService $sukhSyncService)
    {
        $this->sukhSyncService = $sukhSyncService;
    }

    /**
     * Handle the Sukh "created" event.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function created(Sukh $sukh): void
    {
        Log::info('SukhObserver: Sukh created event triggered', [
            'sukh_id' => $sukh->id,
            'sukh_name' => $sukh->name
        ]);

        // Sync with CVSecurity service
        $this->sukhSyncService->syncCreate($sukh);
    }

    /**
     * Handle the Sukh "updated" event.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function updated(Sukh $sukh): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($sukh->wasChanged(Sukh::CODE) && count($sukh->getChanges()) === 1) {
            Log::debug('SukhObserver: Skipping sync for code-only update', [
                'sukh_id' => $sukh->id
            ]);
            return;
        }

        Log::info('SukhObserver: Sukh updated event triggered', [
            'sukh_id' => $sukh->id,
            'sukh_name' => $sukh->name,
            'changed_fields' => array_keys($sukh->getChanges())
        ]);

        // Sync with CVSecurity service
        $this->sukhSyncService->syncUpdate($sukh);
    }

    /**
     * Handle the Sukh "deleted" event.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function deleted(Sukh $sukh): void
    {
        Log::info('SukhObserver: Sukh deleted event triggered', [
            'sukh_id' => $sukh->id,
            'sukh_name' => $sukh->name,
            'cv_code' => $sukh->code
        ]);

        // Sync with CVSecurity service
        $this->sukhSyncService->syncDelete($sukh);
    }

    /**
     * Handle the Sukh "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function retrieved(Sukh $sukh): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->sukhSyncService->syncRead($sukh);
    }

    /**
     * Handle the Sukh "restoring" event.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function restoring(Sukh $sukh): void
    {
        Log::info('SukhObserver: Sukh restoring event triggered', [
            'sukh_id' => $sukh->id,
            'sukh_name' => $sukh->name
        ]);
    }

    /**
     * Handle the Sukh "restored" event.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function restored(Sukh $sukh): void
    {
        Log::info('SukhObserver: Sukh restored event triggered', [
            'sukh_id' => $sukh->id,
            'sukh_name' => $sukh->name
        ]);

        // Treat restoration as creation for CVSecurity
        $this->sukhSyncService->syncCreate($sukh);
    }

    /**
     * Handle the Sukh "force deleted" event.
     *
     * @param Sukh $sukh
     * @return void
     */
    public function forceDeleted(Sukh $sukh): void
    {
        Log::info('SukhObserver: Sukh force deleted event triggered', [
            'sukh_id' => $sukh->id,
            'sukh_name' => $sukh->name,
            'cv_code' => $sukh->code
        ]);

        // Sync with CVSecurity service
        $this->sukhSyncService->syncDelete($sukh);
    }
}
