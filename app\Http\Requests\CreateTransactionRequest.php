<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreateTransactionRequest extends FormRequest
{
    const PARAMETER_INVOICE_ID = 'invoice_id';
    const PARAMETER_IS_ORG     = 'is_org';
    const PARAMETER_VAT_INFO   = 'vat_info';

    public function rules(): array
    {
        return [
            'invoice_id' => 'required|numeric',
            'is_org'     => 'required|boolean',
            'vat_info'   => 'nullable|string',
        ];
    }
}
