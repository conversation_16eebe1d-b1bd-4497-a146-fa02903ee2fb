<?php

namespace App\Filament\Resources\SuperAdmin\PackageMemberResource\Pages;

use App\Filament\Resources\SuperAdmin\PackageMemberResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListPackageMembers extends ListRecords
{
    protected static string $resource = PackageMemberResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
