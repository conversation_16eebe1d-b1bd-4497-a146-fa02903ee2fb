<?php

namespace App\Models;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Laravel\Sanctum\HasApiTokens;

/**
 * App\Models\SBUser
 *
 * @property int $id
 * @property string $username
 * @property mixed $password
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Laravel\Sanctum\PersonalAccessToken> $tokens
 * @property-read int|null $tokens_count
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser query()
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser wherePassword($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser whereRememberToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|SBUser whereUsername($value)
 * @mixin \Eloquent
 */
class SBUser extends Authenticatable
{
    use HasApiTokens, HasFactory;

    const ID         = 'id';
    const USERNAME   = 'username';
    const PASSWORD   = 'password';

    const HINT = 'hint';

    protected $casts = [
        self::PASSWORD => 'hashed',
    ];

    protected $fillable = [
        self::USERNAME,
        self::PASSWORD,
    ];
}
