<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Constant\ConstData;
use App\Http\Requests\LoginRequest;
use App\Http\Resources\Token as TokenResource;
use App\Services\UserInfoService;
use Illuminate\Support\Facades\Hash;

class UserController extends Controller
{
    // public function login(LoginRequest $request)
    // {
    //     $userName = $request->input(LoginRequest::PARAMETER_USER_NAME);
    //     $password = $request->input(LoginRequest::PARAMETER_PASSWORD);

    //     $user = User::where('email', $userName)->whereHas('roles', function ($query) {
    //         $query->where('name', ConstData::ROLE_ADMIN);
    //     })->first();

    //     if (!$user || !Hash::check($password, $user->password)) {
    //         return response()->json(['error' => 'Invalid credentials'], 401);
    //     }

    //     return new TokenResource($user->createToken($user->id));
    // }

    // public function aubairs()
    // {
    //     return resolve(UserInfoService::class)->getAUBairs();
    // }

    // public function auorcs($bairId)
    // {
    //     return resolve(UserInfoService::class)->getAUOrcs($bairId);
    // }
}
