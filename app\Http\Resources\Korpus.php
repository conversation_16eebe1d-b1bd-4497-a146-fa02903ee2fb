<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class Korpus extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'         => $this->id,
            'bair_id'    => $this->bair_id,
            'name'       => $this->name,
            'code'       => $this->code,
            'bair'       => $this->bair ? new Bair($this->bair) : null,
        ];
    }
}
