<?php

namespace Tests\Feature;

use App\Models\OrshinSuugch;
use App\Services\CvSecurityService\CvSecurityService;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use App\Services\OrshinSuugchSyncService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class OrshinSuugchCvSecurityIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set up test configuration
        config([
            'services.cv_security.host' => '127.0.0.1',
            'services.cv_security.port' => '8098',
            'services.cv_security.api_key' => 'test-api-key',
            'services.cv_security_ext.host' => '127.0.0.1',
            'services.cv_security_ext.port' => '8000',
            'services.cv_security_ext.api_key' => 'test-ext-api-key'
        ]);
    }

    /** @test */
    public function orshin_suugch_model_has_code_field_in_fillable()
    {
        $orshinSuugch = new OrshinSuugch();
        $fillable = $orshinSuugch->getFillable();

        $this->assertContains('code', $fillable);
    }

    /** @test */
    public function orshin_suugch_model_has_code_constant()
    {
        $this->assertEquals('code', OrshinSuugch::CODE);
    }

    /** @test */
    public function orshin_suugch_creation_triggers_cv_security_sync()
    {
        // Mock CVSecurity API responses
        Http::fake([
            '127.0.0.1:8098/*' => Http::sequence()
                ->push(['status' => 'online'], 200) // Health check
                ->push(['data' => ['pin' => '20250127130001123'], 'message' => 'Success'], 200), // Create person
            '127.0.0.1:8000/*' => Http::sequence()
                ->push(['pin' => '20250127130001123'], 200) // Get next pin from EXT service
        ]);

        // Create an OrshinSuugch
        $orshinSuugch = OrshinSuugch::create([
            'name' => 'Test Person',
            'last_name' => 'Test Last Name',
            'phone' => '99887766',
            'email' => '<EMAIL>',
            'is_admin' => false,
        ]);

        $orshinSuugch->refresh();

        // Assert that the code was set
        $this->assertNotNull($orshinSuugch->code);
        $this->assertEquals('20250127130001123', $orshinSuugch->code);

        // Verify the HTTP requests were made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://127.0.0.1:8098/api/person/addPersonnelBasicInfo';
        });
    }

    /** @test */
    public function orshin_suugch_update_triggers_cv_security_sync()
    {
        // Create an OrshinSuugch with a code
        $orshinSuugch = OrshinSuugch::create([
            'name' => 'Test Person',
            'last_name' => 'Test Last Name',
            'phone' => '99887766',
            'email' => '<EMAIL>',
            'is_admin' => false,
            'code' => '20250127130001123',
        ]);

        // Mock CVSecurity API responses
        Http::fake([
            '127.0.0.1:8098/*' => Http::sequence()
                ->push(['status' => 'online'], 200) // Health check
                ->push(['data' => ['pin' => '20250127130001123'], 'message' => 'Updated'], 200) // Update person
        ]);

        // Update the OrshinSuugch
        $orshinSuugch->update(['name' => 'Updated Person Name']);

        // Verify the HTTP request was made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://127.0.0.1:8098/api/person/addPersonnelBasicInfo' &&
                   $request['name'] === 'Updated Person Name';
        });
    }

    /** @test */
    public function orshin_suugch_deletion_triggers_cv_security_sync()
    {
        // Create an OrshinSuugch with a code
        $orshinSuugch = OrshinSuugch::create([
            'name' => 'Test Person',
            'last_name' => 'Test Last Name',
            'phone' => '99887766',
            'email' => '<EMAIL>',
            'is_admin' => false,
            'code' => '20250127130001123',
        ]);

        // Mock CVSecurity API responses
        Http::fake([
            '127.0.0.1:8098/*' => Http::sequence()
                ->push(['status' => 'online'], 200) // Health check
                ->push(['message' => 'Deleted'], 200) // Delete person
        ]);

        // Delete the OrshinSuugch
        $orshinSuugch->delete();

        // Verify the HTTP request was made
        Http::assertSent(function ($request) {
            return $request->url() === 'http://127.0.0.1:8098/api/person/delete/20250127130001123';
        });
    }

    /** @test */
    public function orshin_suugch_sync_handles_cv_security_service_unavailable()
    {
        // Mock CVSecurity service as unavailable
        $mockService = $this->createMock(CvSecurityService::class);
        $mockService->method('isServiceAvailable')->willReturn(false);

        $mockSyncService = $this->createMock(OrshinSuugchSyncService::class);
        $mockSyncService->expects($this->once())->method('syncCreate');

        $this->app->instance(CvSecurityService::class, $mockService);
        $this->app->instance(OrshinSuugchSyncService::class, $mockSyncService);

        // Create an OrshinSuugch
        $orshinSuugch = OrshinSuugch::create([
            'name' => 'Test Person Offline',
            'last_name' => 'Test Last Name',
            'phone' => '99000111',
            'email' => '<EMAIL>',
            'is_admin' => false,
        ]);

        $orshinSuugch->refresh();

        // Assert that the code is null when service is unavailable
        $this->assertNull($orshinSuugch->code);
    }

    /** @test */
    public function orshin_suugch_update_skips_sync_for_code_only_changes()
    {
        // Create an OrshinSuugch
        $orshinSuugch = OrshinSuugch::create([
            'name' => 'Test Person',
            'last_name' => 'Test Last Name',
            'phone' => '99887766',
            'email' => '<EMAIL>',
            'is_admin' => false,
        ]);

        // Mock the sync service to ensure it's not called
        $mockSyncService = $this->createMock(OrshinSuugchSyncService::class);
        $mockSyncService->expects($this->never())->method('syncUpdate');

        $this->app->instance(OrshinSuugchSyncService::class, $mockSyncService);

        // Update only the code field
        $orshinSuugch->update(['code' => '20250127130001123']);
    }

    /** @test */
    public function cv_security_service_can_create_person()
    {
        // Mock successful person creation
        Http::fake([
            '127.0.0.1:8098/api/person/addPersonnelBasicInfo' => Http::response([
                'data' => ['pin' => '20250127130001123'],
                'message' => 'Success'
            ], 200)
        ]);

        $service = new CvSecurityService();

        $personData = [
            'pin' => '20250127130001123',
            'name' => 'Test Person',
            'lastName' => 'Test Last Name',
            'mobilePhone' => '99887766',
            'email' => '<EMAIL>'
        ];

        $response = $service->createOrUpdatePerson($personData);

        $this->assertNotNull($response);
        $this->assertEquals('20250127130001123', $response->data->pin);
    }

    /** @test */
    public function cv_security_service_can_delete_person()
    {
        // Mock successful person deletion
        Http::fake([
            '127.0.0.1:8098/api/person/delete/20250127130001123' => Http::response([
                'message' => 'Deleted successfully'
            ], 200)
        ]);

        $service = new CvSecurityService();
        $response = $service->deletePerson('20250127130001123');

        $this->assertNotNull($response);
        $this->assertEquals('Deleted successfully', $response->message);
    }

    /** @test */
    public function cv_security_service_can_get_person()
    {
        // Mock successful person retrieval
        Http::fake([
            '127.0.0.1:8098/api/person/get/20250127130001123' => Http::response([
                'data' => [
                    'pin' => '20250127130001123',
                    'name' => 'Test Person',
                    'lastName' => 'Test Last Name'
                ]
            ], 200)
        ]);

        $service = new CvSecurityService();
        $response = $service->getPerson('20250127130001123');

        $this->assertNotNull($response);
        $this->assertEquals('20250127130001123', $response->data->pin);
        $this->assertEquals('Test Person', $response->data->name);
    }

    /** @test */
    public function cv_security_service_generates_mock_person_pin()
    {
        $service = new CvSecurityService();
        $pin = $service->getNextPersonPin();

        $this->assertNotNull($pin);
        $this->assertIsString($pin);
        $this->assertEquals(17, strlen($pin)); // YmdHis (14) + 3 digit suffix
    }

    /** @test */
    public function cv_security_ext_service_can_get_next_person_pin()
    {
        // Mock successful next pin retrieval from EXT service
        Http::fake([
            '127.0.0.1:8000/persons/next-pin' => Http::response([
                'pin' => '1001'
            ], 200)
        ]);

        $service = new CvSecurityServiceExt();
        $pin = $service->getNextPersonPin();

        $this->assertNotNull($pin);
        $this->assertEquals('1001', $pin);
    }

    /** @test */
    public function cv_security_ext_service_handles_failed_next_pin_request()
    {
        // Mock failed next pin retrieval
        Http::fake([
            '127.0.0.1:8000/persons/next-pin' => Http::response([], 500)
        ]);

        $service = new CvSecurityServiceExt();
        $pin = $service->getNextPersonPin();

        $this->assertNull($pin);
    }
}
