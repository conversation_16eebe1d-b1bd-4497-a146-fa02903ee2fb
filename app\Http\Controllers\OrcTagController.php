<?php

namespace App\Http\Controllers;

use App\Models\Orc;
use App\Models\OrcTag;
use App\Models\DeviceDtl;
use App\Enums\CQPEnum;
use App\Http\Requests\StoreOrcTagRequest;
use App\Http\Resources\OrcTag as OrcTagResource;
use App\Services\ChirpStackService;

class OrcTagController extends Controller
{
    public function index()
    {
        $orcTags = OrcTag::all();
        return OrcTagResource::collection($orcTags);
    }

    public function store(StoreOrcTagRequest $request)
    {
        $orcId  = $request->input(StoreOrcTagRequest::PARAMETER_ORC_ID);
        $code   = $request->input(StoreOrcTagRequest::PARAMETER_CODE);
        $orcTag = OrcTag::create([
            OrcTag::ORC_ID   => $orcId,
            OrcTag::TAG_CODE => $code
        ]);

        $orc       = Orc::find($orcId);
        $deviceDtl = DeviceDtl::where(DeviceDtl::KORPUS_ID, $orc->korpus_id)->where(DeviceDtl::ORC_ID, $orcId)->first();

        $data         = base64_encode('1;'.$code);
        $chirpService = resolve(ChirpStackService::class);
        $chirpService->postDeviceQueue($deviceDtl->dev_eui, $data, CQPEnum::NFC);

        return new OrcTagResource($orcTag);
    }

    public function destroy(int $orcTagId)
    {
        $orcTag    = OrcTag::find($orcTagId);
        $orc       = Orc::find($orcTag->orc_id);
        $deviceDtl = DeviceDtl::where(DeviceDtl::KORPUS_ID, $orc->korpus_id)->where(DeviceDtl::ORC_ID, $orcTag->orc_id)->first();

        $data         = base64_encode('1;'.$orcTag->tag_code);
        $chirpService = resolve(ChirpStackService::class);
        $chirpService->postDeviceQueue($deviceDtl->dev_eui, $data, CQPEnum::NFC);

        return $orcTag->delete();
    }

}
