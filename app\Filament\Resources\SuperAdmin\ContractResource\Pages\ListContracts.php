<?php

namespace App\Filament\Resources\SuperAdmin\ContractResource\Pages;

use App\Filament\Resources\SuperAdmin\ContractResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContracts extends ListRecords
{
    protected static string $resource = ContractResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
