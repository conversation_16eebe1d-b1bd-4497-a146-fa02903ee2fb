# Dynamic Door Numbering Preview Component

## Overview

The Dynamic Door Numbering Preview Component provides real-time visualization of how floors (Davhars) and doors (Toots) will be generated based on configuration parameters. This component shows a live preview that updates automatically when any configuration field changes, without persisting data to the database until the user explicitly saves.

## Features

### Core Functionality

-   **Real-time Updates**: Preview updates automatically when configuration parameters change
-   **Hierarchical Display**: Shows Korpus → Orc → Floor → Door structure
-   **Door Range Display**: Shows door number ranges (e.g., "101-106") for each floor
-   **Prominent Notice**: Clear indication that this is preview-only data
-   **Error Handling**: Displays validation errors and configuration issues

### Supported Numbering Types

#### Type 1: Korpus-wise Consecutive Numbering

-   All doors in the building are numbered consecutively
-   Example: Orc 1 gets doors 1-12, Orc 2 gets doors 13-24

#### Type 2: Orc-wise Consecutive Numbering

-   Each Orc's doors start numbering from 1
-   Example: Both Orc 1 and Orc 2 have doors 1-12

#### Type 3: Floor-wise Consecutive Numbering

-   Door numbers include floor number as prefix
-   Supports 2-digit (11-16), 3-digit (101-106), or 4-digit (1001-1006) formats
-   Example: Floor 1 = 101-106, Floor 2 = 201-206

## Implementation

### Service Layer

**DoorNumberingPreviewService** (`app/Services/DoorNumberingPreviewService.php`)

-   Generates preview data without database persistence
-   Handles all three numbering types with proper calculations
-   Validates configuration parameters
-   Returns structured data for UI display

### Component Layer

**DoorNumberingPreview** (`app/Filament/Components/DoorNumberingPreview.php`)

-   Filament form component for real-time preview
-   Integrates with DoorNumberingPreviewService
-   Handles form state changes and updates
-   Provides reactive functionality

### View Layer

**door-numbering-preview.blade.php** (`resources/views/filament/components/door-numbering-preview.blade.php`)

-   Responsive UI layout with hierarchical structure
-   Prominent preview notice with warning styling
-   Grid layout for floor/door range display
-   Summary section with total door count

## Usage

### In Filament Forms

```php
use App\Filament\Components\DoorNumberingPreview;

// Add to form schema
DoorNumberingPreview::make('door_numbering_preview')
    ->label('Хаалганы дугаарлалтын урьдчилан харах')
    ->reactive()
    ->enableRealTimeUpdates()
    ->columnSpanFull()
```

### Required Form Fields

The preview component automatically reads these form fields:

-   `numbering_type` - Door numbering type (1, 2, or 3)
-   `digit_multiplier` - For Type 3: 10, 100, or 1000
-   `floors_count` - Default number of floors per Orc
-   `doors_per_floor` - Number of doors per floor
-   `orcs` - Array of Orc configurations

### Making Fields Reactive

Add `->live()` to form fields to trigger preview updates:

```php
Forms\Components\Select::make('numbering_type')
    ->options([...])
    ->live(), // Triggers preview update

Forms\Components\TextInput::make('floors_count')
    ->numeric()
    ->live(), // Triggers preview update
```

## Preview Data Structure

The service returns structured data for the UI:

```php
[
    'type' => 1,
    'type_name' => 'Төрөл 1: Блокийн дугаарлалт',
    'description' => 'Бүх блокийн хаалганууд дараалан дугаарлагдана',
    'orcs' => [
        [
            'number' => 1,
            'floors_count' => 2,
            'total_doors' => 12,
            'floors' => [
                [
                    'number' => 1,
                    'door_range' => '1-6',
                    'door_count' => 6
                ],
                [
                    'number' => 2,
                    'door_range' => '7-12',
                    'door_count' => 6
                ]
            ]
        ]
    ],
    'total_doors' => 24,
    'digit_multiplier' => 100
]
```

## Integration Example

The component is integrated into the KorpusesRelationManager:

```php
// In form schema
Forms\Components\Repeater::make('orcs')
    ->schema([...])
    ->live(), // Make repeater reactive

// Add preview component
DoorNumberingPreview::make('door_numbering_preview')
    ->label('Хаалганы дугаарлалтын урьдчилан харах')
    ->reactive()
    ->enableRealTimeUpdates()
    ->columnSpanFull(),
```

## Testing

Comprehensive unit tests verify:

-   All three numbering types work correctly
-   Error handling for invalid configurations
-   Floor count overrides per Orc
-   Different digit multiplier formats
-   Door range calculations

Run tests with:

```bash
vendor/bin/phpunit tests/Unit/DoorNumberingPreviewServiceTest.php
```

## Benefits

1. **User Experience**: Real-time feedback prevents configuration errors
2. **Data Safety**: No database changes until explicit save action
3. **Clarity**: Visual representation of complex numbering logic
4. **Validation**: Immediate feedback on configuration issues
5. **Flexibility**: Supports all door numbering types and configurations

## Future Enhancements

-   Export preview data to PDF/Excel
-   Bulk configuration templates
-   Advanced validation rules
-   Integration with CVSecurity preview
-   Mobile-responsive improvements
