<?php

namespace App\Http\Tools;

use Carbon\Carbon;

class DateTool
{
    public static function nowDate() {
        return Carbon::now()->format('Y-m-d');
    }

    public static function nowDateTime() {
        return Carbon::now()->format('Y-m-d H:i:s');
    }

    public static function getLeftDaysFromEndDates($endDate) {
        $nowDate     = self::nowDate();
        $cbEndDate   = Carbon::parse($endDate);
        return $cbEndDate->diffInDays($nowDate);
    }

    public static function getLeftHoursFromEndDateTime($endDateTime) {
        $nowDate     = self::nowDateTime();
        $cbEndDate   = Carbon::parse($endDateTime);
        return $cbEndDate->diffInHours($nowDate);
    }

    public static function getDaysFromBetweenDates($beginDate, $endDate) {
        $cbBeginDate = Carbon::parse($beginDate);
        $cbEndDate   = Carbon::parse($endDate);
        return $cbEndDate->diffInDays($cbBeginDate);
    }
}

