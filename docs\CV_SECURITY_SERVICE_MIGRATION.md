# CV Security Service Migration Documentation

## Overview

This document describes the migration of the CV_SECURITY service configuration to support both the existing functionality (renamed to CV_SECURITY_EXT) and a new CV_SECURITY service for future implementation.

## Changes Made

### 1. Environment Variables

**Before:**
- `CV_SECURITY_HOST` - Used by existing service
- `CV_SECURITY_PORT` - Used by existing service
- `CV_SECURITY_API_KEY` - Shared API key

**After:**
- `CV_SECURITY_HOST` - New service (future implementation)
- `CV_SECURITY_PORT` - New service (future implementation)
- `CV_SECURITY_EXT_HOST` - Existing service (renamed)
- `CV_SECURITY_EXT_PORT` - Existing service (renamed)
- `CV_SECURITY_API_KEY` - Shared API key for both services

### 2. Configuration Constants (ConstData.php)

Added new constants:
```php
const CV_SECURITY_HOST            = 'CV_SECURITY_HOST';
const CV_SECURITY_PORT            = 'CV_SECURITY_PORT';
const CV_SECURITY_EXT_HOST        = 'CV_SECURITY_EXT_HOST';
const CV_SECURITY_EXT_PORT        = 'CV_SECURITY_EXT_PORT';
const CV_SECURITY_API_KEY         = 'CV_SECURITY_API_KEY';
```

### 3. Service Configuration (config/services.php)

**Before:**
```php
'cv_security' => [
    'host'    => env(ConstData::CV_SECURITY_HOST),
    'port'    => env(ConstData::CV_SECURITY_PORT),
    'api_key' => env(ConstData::CV_SECURITY_API_KEY),
],
```

**After:**
```php
'cv_security' => [
    'host'    => env(ConstData::CV_SECURITY_HOST),
    'port'    => env(ConstData::CV_SECURITY_PORT),
    'api_key' => env(ConstData::CV_SECURITY_API_KEY),
],

'cv_security_ext' => [
    'host'    => env(ConstData::CV_SECURITY_EXT_HOST),
    'port'    => env(ConstData::CV_SECURITY_EXT_PORT),
    'api_key' => env(ConstData::CV_SECURITY_API_KEY),
],
```

### 4. Service Classes

#### CvSecurityServiceExt (Existing - Renamed)
- **Location:** `app/Services/CvSecurityService/CvSecurityServiceExt.php`
- **Purpose:** Maintains existing functionality for sukhs, korpuses, and orcs synchronization
- **Configuration:** Uses `services.cv_security_ext.*` configuration
- **Changes:** Renamed from `CvSecurityService` and updated to use `cv_security_ext` configuration

#### CvSecurityService (New)
- **Location:** `app/Services/CvSecurityService/CvSecurityService.php`
- **Purpose:** Placeholder for new CV_SECURITY service implementation
- **Configuration:** Uses `services.cv_security.*` configuration
- **Status:** Ready for future implementation

### 5. Service Registration (AppServiceProvider.php)

Both services are now registered:
```php
// Register CvSecurityService (EXT - existing functionality)
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityService::class);

// Register CvSecurityNewService (new CV_SECURITY service for future implementation)
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityNewService::class);
```

## Migration Impact

### ✅ Preserved Functionality
- All existing CRUD synchronization for Sukhs, Korpuses, and Orcs continues to work
- All existing observers and sync services remain unchanged
- All existing API endpoints and methods are preserved
- All existing documentation remains valid (with updated environment variable names)

### ✅ New Capabilities
- New CV_SECURITY service ready for future implementation
- Clear separation between existing (EXT) and new services
- Both services can run simultaneously on different ports
- Shared API key for authentication

### ⚠️ Required Environment Updates

Update your `.env` file to include the new variables:
```env
# CV Security Services Configuration
# CV_SECURITY: New service for future implementation
CV_SECURITY_HOST=*************
CV_SECURITY_PORT=8098

# CV_SECURITY_EXT: Existing service (renamed from CV_SECURITY)
CV_SECURITY_EXT_HOST=*************
CV_SECURITY_EXT_PORT=8078

# Shared API key for both services
CV_SECURITY_API_KEY=1234567890abcdef
```

## Testing

### Verify Existing Functionality
1. Test Sukh CRUD operations and CV Security synchronization
2. Test Korpus CRUD operations and building synchronization
3. Test Orc CRUD operations and entrance synchronization
4. Run existing test commands:
   ```bash
   php artisan test:sukh-cv-security status
   php artisan test:korpus-cv-security status
   ```

### Verify New Service
1. Check new service status:
   ```php
   $newService = app(\App\Services\CvSecurityService\CvSecurityNewService::class);
   $status = $newService->getServiceStatus();
   $info = $newService->getInfo();
   ```

## Future Implementation

The new `CvSecurityNewService` is ready for implementation. When implementing:

1. Add specific methods for the new service requirements
2. Update the placeholder methods with actual functionality
3. Add appropriate error handling and logging
4. Create corresponding sync services if needed
5. Update documentation with new service capabilities

## Rollback Plan

If rollback is needed:
1. Revert `CvSecurityService.php` to use `cv_security` configuration
2. Remove `CvSecurityNewService.php`
3. Remove `cv_security_ext` from `config/services.php`
4. Remove EXT constants from `ConstData.php`
5. Update environment variables back to original names
