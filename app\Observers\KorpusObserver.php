<?php

namespace App\Observers;

use App\Models\Korpus;
use App\Services\KorpusSyncService;
use Illuminate\Support\Facades\Log;

class KorpusObserver
{
    protected KorpusSyncService $korpusSyncService;

    public function __construct(KorpusSyncService $korpusSyncService)
    {
        $this->korpusSyncService = $korpusSyncService;
    }

    /**
     * Handle the Korpus "created" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function created(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus created event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncCreate($korpus);
    }

    /**
     * Handle the Korpus "updated" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function updated(Korpus $korpus): void
    {
        // Skip sync if only the code field was updated (to avoid infinite loops)
        if ($korpus->wasChanged(Korpus::CODE) && count($korpus->getChanges()) === 1) {
            Log::debug('KorpusObserver: Skipping sync for code-only update', [
                'korpus_id' => $korpus->id
            ]);
            return;
        }

        Log::info('KorpusObserver: Korpus updated event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name,
            'changed_fields' => array_keys($korpus->getChanges())
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncUpdate($korpus);
    }

    /**
     * Handle the Korpus "deleted" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function deleted(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus deleted event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name,
            'cv_code' => $korpus->code
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncDelete($korpus);
    }

    /**
     * Handle the Korpus "retrieved" event.
     * This can be used for read operations if needed.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function retrieved(Korpus $korpus): void
    {
        // Optionally sync read operations
        // Uncomment the line below if you want to sync on every read
        // $this->korpusSyncService->syncRead($korpus);
    }

    /**
     * Handle the Korpus "restoring" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function restoring(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus restoring event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name
        ]);
    }

    /**
     * Handle the Korpus "restored" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function restored(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus restored event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name
        ]);

        // Treat restoration as creation for CVSecurity
        $this->korpusSyncService->syncCreate($korpus);
    }

    /**
     * Handle the Korpus "force deleted" event.
     *
     * @param Korpus $korpus
     * @return void
     */
    public function forceDeleted(Korpus $korpus): void
    {
        Log::info('KorpusObserver: Korpus force deleted event triggered', [
            'korpus_id' => $korpus->id,
            'korpus_name' => $korpus->name,
            'cv_code' => $korpus->code
        ]);

        // Sync with CVSecurity service
        $this->korpusSyncService->syncDelete($korpus);
    }
}
