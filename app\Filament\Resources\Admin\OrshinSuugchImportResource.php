<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\OrshinSuugchImportResource\Pages;
use App\Models\Constant\ConstData;
use App\Models\OrshinSuugchImport;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Filament\Forms\Components\FileUpload;

class OrshinSuugchImportResource extends Resource
{
    protected static ?string $model = OrshinSuugchImport::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
    protected static ?string $navigationLabel = 'Оршин суугч импорт';
    protected static ?string $modelLabel = 'оршин суугч';
    protected static ?string $pluralModelLabel = 'оршин суугч импорт';
    protected static ?int $navigationSort = 2;
    protected static ?string $slug = 'orshinsuugchimports';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make(ConstData::ATTACHMENT)
                    ->storeFiles(false)
                    ->minFiles(1)
                    ->acceptedFileTypes(['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet','application/vnd.ms-excel', 'application/vnd.oasis.opendocument.spreadsheet'])
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make(OrshinSuugchImport::ID)->label('Код')->sortable(),
                Tables\Columns\TextColumn::make(OrshinSuugchImport::STATUS)->label('Төлөв')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(OrshinSuugchImport::MESSAGE)->label('Зурвас')->sortable()->searchable(),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrshinSuugchImports::route('/'),
            'create' => Pages\CreateOrshinSuugchImport::route('/create'),
            'edit' => Pages\EditOrshinSuugchImport::route('/{record}/edit'),
        ];
    }
}
