<?php

namespace App\Filament\Widgets;

use App\Services\CvSecurityService\CvSecurityService;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class CvSecurityInternalStatusWidget extends BaseWidget
{
    protected static ?int $sort = 2;
    
    protected static ?string $pollingInterval = '30s';

    protected function getStats(): array
    {
        try {
            $cvSecurityService = resolve(CvSecurityService::class);
            $status = $cvSecurityService->getServiceStatus();

            $color = $status['status'] === 'online' ? 'success' : 'danger';
            $icon = $status['status'] === 'online' ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle';
            $description = $status['host'] . ':' . $status['port'];

            return [
                Stat::make('CV Security', ucfirst($status['status']))
                    ->description($description)
                    ->descriptionIcon($icon)
                    ->color($color),
            ];
        } catch (\Exception $e) {
            return [
                Stat::make('CV Security', 'Error')
                    ->description('Service check failed')
                    ->descriptionIcon('heroicon-o-exclamation-triangle')
                    ->color('danger'),
            ];
        }
    }
}
