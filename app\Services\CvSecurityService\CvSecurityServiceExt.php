<?php

namespace App\Services\CvSecurityService;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * CV Security Service Ext
 * This is the existing CV Security service that handles sukhs, korpuses, and orcs synchronization.
 * It uses the cv_security_ext configuration.
 */
class CvSecurityServiceExt
{
    /**
     * Check if CV Security service is available
     * Uses the root endpoint "/" as a health check
     *
     * @return bool
     */
    public function isServiceAvailable(): bool
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            if (!$host || !$port) {
                Log::warning('CV Security EXT service configuration is incomplete');
                return false;
            }

            $url = "http://{$host}:{$port}/";

            // Try root endpoint first (usually doesn't require auth)
            $response = Http::timeout(5)->get($url);

            // If root endpoint fails, try with authentication
            if (!$response->successful() && $apiKey) {
                $response = Http::timeout(5)
                    ->withToken($apiKey)
                    ->get($url);
            }

            return $response->successful();

        } catch (\Exception $e) {
            Log::error('CV Security EXT service health check failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get service status with details
     *
     * @return array
     */
    public function getServiceStatus(): array
    {
        $isAvailable = $this->isServiceAvailable();

        return [
            'service' => 'CV Security EXT',
            'status' => $isAvailable ? 'online' : 'offline',
            'color' => $isAvailable ? 'success' : 'danger',
            'icon' => $isAvailable ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle',
            'host' => config('services.cv_security_ext.host'),
            'port' => config('services.cv_security_ext.port'),
        ];
    }

    /**
     * Create a new Sukh (Residents' Committee)
     *
     * @param array $data
     * @return object|null
     */
    public function createSukh(array $data): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT createSukh failed: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createSukh exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Sukh by code
     *
     * @param string $code
     * @return object|null
     */
    public function getSukhByCode(string $code): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getSukhByCode exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update Sukh by code
     *
     * @param string $code
     * @param array $data
     * @return object|null
     */
    public function updateSukh(string $code, array $data): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT updateSukh failed: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateSukh exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete Sukh by code
     *
     * @param string $code
     * @return bool
     */
    public function deleteSukh(string $code): bool
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            return $response->status() === 204;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteSukh exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get next available Sukh code
     *
     * @return string|null
     */
    public function getNextSukhCode(): ?string
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/sukhs/next-code";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);
                return $data['code'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextSukhCode exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a new Building (Korpus)
     *
     * @param array $data
     * @return object|null
     */
    public function createBuilding(array $data): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT createBuilding failed: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createBuilding exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Building by code
     *
     * @param string $code
     * @return object|null
     */
    public function getBuildingByCode(string $code): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getBuildingByCode exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update Building by code
     *
     * @param string $code
     * @param array $data
     * @return object|null
     */
    public function updateBuilding(string $code, array $data): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT updateBuilding failed: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateBuilding exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete Building by code
     *
     * @param string $code
     * @return bool
     */
    public function deleteBuilding(string $code): bool
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            return $response->status() === 204;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteBuilding exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get next available Building code
     *
     * @return string|null
     */
    public function getNextBuildingCode(): ?string
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/buildings/next-code";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);
                return $data['code'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextBuildingCode exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a new Entrance (Orc)
     *
     * @param array $data
     * @return object|null
     */
    public function createEntrance(array $data): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT createEntrance failed: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createEntrance exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get Entrance by code
     *
     * @param string $code
     * @return object|null
     */
    public function getEntranceByCode(string $code): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getEntranceByCode exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update Entrance by code
     *
     * @param string $code
     * @param array $data
     * @return object|null
     */
    public function updateEntrance(string $code, array $data): ?object
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body());
            }

            Log::error('CV Security EXT updateEntrance failed: ' . $response->body());
            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateEntrance exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete Entrance by code
     *
     * @param string $code
     * @return bool
     */
    public function deleteEntrance(string $code): bool
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            return $response->status() === 204;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteEntrance exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get next available Entrance code
     *
     * @return string|null
     */
    public function getNextEntranceCode(): ?string
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/entrances/next-code";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);
                return $data['code'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextEntranceCode exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get next available Person PIN
     *
     * @return string|null
     */
    public function getNextPersonPin(): ?string
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/persons/next-pin";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                $data = json_decode($response->body(), true);
                return $data['pin'] ?? null;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getNextPersonPin exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Create EleLevel (Floor) in CVSecurity
     *
     * @param array $data
     * @return array|null
     */
    public function createEleLevel(array $data): ?array
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/";

            $response = Http::withToken($apiKey)
                ->post($url, $data);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT createEleLevel failed', [
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT createEleLevel exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Update EleLevel (Floor) in CVSecurity
     *
     * @param string $code
     * @param array $data
     * @return array|null
     */
    public function updateEleLevel(string $code, array $data): ?array
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/{$code}";

            $response = Http::withToken($apiKey)
                ->put($url, $data);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT updateEleLevel failed', [
                'code' => $code,
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT updateEleLevel exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Delete EleLevel (Floor) from CVSecurity
     *
     * @param string $code
     * @return bool
     */
    public function deleteEleLevel(string $code): bool
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/{$code}";

            $response = Http::withToken($apiKey)
                ->delete($url);

            if ($response->successful()) {
                return true;
            }

            Log::error('CV Security EXT deleteEleLevel failed', [
                'code' => $code,
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('CV Security EXT deleteEleLevel exception: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get EleLevel (Floor) by code from CVSecurity
     *
     * @param string $code
     * @return array|null
     */
    public function getEleLevelByCode(string $code): ?array
    {
        try {
            $host = config('services.cv_security_ext.host');
            $port = config('services.cv_security_ext.port');
            $apiKey = config('services.cv_security_ext.api_key');

            $url = "http://{$host}:{$port}/ele_levels/{$code}";

            $response = Http::withToken($apiKey)
                ->get($url);

            if ($response->successful()) {
                return json_decode($response->body(), true);
            }

            Log::error('CV Security EXT getEleLevelByCode failed', [
                'code' => $code,
                'status' => $response->status(),
                'body' => $response->body()
            ]);

            return null;

        } catch (\Exception $e) {
            Log::error('CV Security EXT getEleLevelByCode exception: ' . $e->getMessage());
            return null;
        }
    }
}
