<?php

namespace App\Filament\Resources\SuperAdmin\ContractTemplateTypeResource\Pages;

use App\Filament\Resources\SuperAdmin\ContractTemplateTypeResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListContractTemplateTypes extends ListRecords
{
    protected static string $resource = ContractTemplateTypeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
