<?php

namespace App\Filament\Resources\Admin\NehemjlehTohirgooResource\Pages;

use App\Filament\Resources\Admin\NehemjlehTohirgooResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListNehemjlehTohirgoos extends ListRecords
{
    protected static string $resource = NehemjlehTohirgooResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make()->label('Нэмэх'),
        ];
    }
}
