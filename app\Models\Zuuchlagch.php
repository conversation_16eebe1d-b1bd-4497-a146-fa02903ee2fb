<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Zuuchlagch
 *
 * @mixin IdeHelperZuuchlagch
 * @property int $id
 * @property string $last_name
 * @property string $first_name
 * @property string|null $phone
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read mixed $name
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch query()
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch whereFirstName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch whereLastName($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch wherePhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Zuuchlagch whereUpdatedAt($value)
 * @mixin \Eloquent
 */
class Zuuchlagch extends Model
{
    use HasFactory;

    const ID                 = 'id';
    const LAST_NAME          = 'last_name';
    const FIRST_NAME         = 'first_name';
    const PHONE              = 'phone';

    const NAME               = 'name';

    protected $fillable = [
        self::ID,
        self::LAST_NAME,
        self::FIRST_NAME,
        self::PHONE,
    ];

    public function getNameAttribute()
    {
        return "$this->last_name $this->first_name";
    }
}
