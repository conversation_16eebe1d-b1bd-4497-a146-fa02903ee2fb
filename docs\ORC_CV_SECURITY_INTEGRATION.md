# Orc CVSecurity Integration

This document describes the integration between the Orc model and the CVSecurity service, which automatically synchronizes entrance data between the application and the external CVSecurity system.

## Overview

The integration provides automatic synchronization of Orc (entrance) data with the CVSecurity service through:

-   **Automatic CRUD synchronization**: Create, update, and delete operations are automatically synced
-   **Entrance name construction**: Entrance names are constructed as "Орц {number}"
-   **Parent Korpus association**: Entrances are linked to their parent Korpus (building)
-   **Error handling**: Graceful handling of service unavailability
-   **Logging**: Comprehensive logging of all sync operations

## Architecture

The integration consists of several components:

1. **OrcObserver** (`app/Observers/OrcObserver.php`)

    - Listens to Orc model events (created, updated, deleted)
    - Triggers appropriate CVSecurity sync operations

2. **OrcSyncService** (`app/Services/OrcSyncService.php`)

    - Handles the actual synchronization with CVSecurity
    - Manages error handling and logging
    - Extracts codes from CVSecurity responses
    - Constructs entrance names and parent relationships

3. **CvSecurityServiceExt** (`app/Services/CvSecurityService/CvSecurityServiceExt.php`)

    - Extended with entrance-related methods
    - Provides CRUD operations for Entrances in CVSecurity

4. **Database Migration** (`database/migrations/2025_01_27_120000_add_code_to_orcs_table.php`)
    - Adds the `code` field to the orcs table

## Database Schema Changes

### New Field: `code`

-   **Type**: `string`, nullable
-   **Purpose**: Stores the CVSecurity response code
-   **Location**: Added after `end_toot_number` field in orcs table

```sql
ALTER TABLE orcs ADD COLUMN code VARCHAR(255) NULL AFTER end_toot_number;
```

## Configuration

### Environment Variables

```env
CV_SECURITY_HOST=127.0.0.1
CV_SECURITY_PORT=8001
CV_SECURITY_API_KEY=1234567890abcdef
```

### Service Registration

The services are registered in `app/Providers/AppServiceProvider.php`:

```php
// Register CvSecurityServiceExt
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityServiceExt::class);

// Register OrcSyncService
$this->app->singleton(\App\Services\OrcSyncService::class);
```

### Observer Registration

The observer is registered in `app/Providers/AppServiceProvider.php`:

```php
// Register model observers
Orc::observe(OrcObserver::class);
```

## Usage

### Automatic Synchronization

The integration works automatically. When you perform any CRUD operation on an Orc:

```php
// Create - automatically syncs with CVSecurity
$orc = Orc::create([
    'korpus_id' => 1,
    'number' => 'A1',
    'begin_toot_number' => 1,
    'end_toot_number' => 10,
]);

// Update - automatically syncs with CVSecurity
$orc->update([
    'number' => 'A2',
]);

// Delete - automatically syncs with CVSecurity
$orc->delete();
```

### Manual Synchronization

You can also manually trigger synchronization:

```php
use App\Services\OrcSyncService;

$orcSyncService = app(OrcSyncService::class);

// Manual sync for creation
$orcSyncService->syncCreate($orc);

// Manual sync for update
$orcSyncService->syncUpdate($orc);

// Manual sync for deletion
$orcSyncService->syncDelete($orc);
```

## Entity Mapping

-   **Local Entity**: Orc (entrance)
-   **CVSecurity Entity**: Entrance
-   **Parent Relationship**: Korpus code is used as parent_code in CVSecurity
-   **Name Format**: "Орц {number}"

## Data Flow

1. **Create Operation**:

    - Orc created in local database
    - OrcObserver triggers syncCreate
    - OrcSyncService prepares data with parent Korpus code
    - CVSecurity createEntrance API called
    - Response code stored in Orc.code field

2. **Update Operation**:

    - Orc updated in local database
    - OrcObserver triggers syncUpdate
    - OrcSyncService prepares updated data
    - CVSecurity updateEntrance API called
    - Response code updated in Orc.code field

3. **Delete Operation**:
    - Orc deleted from local database
    - OrcObserver triggers syncDelete
    - CVSecurity deleteEntrance API called

## Error Handling

-   **Service Unavailable**: Operations continue, code field set to null
-   **API Errors**: Logged with full context, code field set to null
-   **Exceptions**: Caught and logged, operations don't fail
-   **Infinite Loops**: Prevented by checking changed fields

## Logging

All operations are logged with appropriate levels:

```
[INFO] OrcObserver: Orc created event triggered
[INFO] Orc successfully synced with CVSecurity on creation
[WARNING] CVSecurity service is not available during Orc creation
[ERROR] CVSecurity create operation failed for Orc
```

## API Endpoints Used

-   `POST /entrances/` - Create entrance
-   `GET /entrances/{code}` - Get entrance by code
-   `PUT /entrances/{code}` - Update entrance
-   `DELETE /entrances/{code}` - Delete entrance
-   `GET /entrances/next-code` - Get next available code

## Troubleshooting

### Common Issues

1. **Code field not populated**

    - Check CVSecurity service status
    - Verify environment variables are set correctly
    - Check logs for error messages

2. **Observer not triggering**

    - Ensure observer is registered in AppServiceProvider
    - Clear application cache: `php artisan cache:clear`
    - Restart application server

3. **Parent relationship issues**
    - Ensure Korpus has a valid code field
    - Check that Korpus is properly synced with CVSecurity first

### Testing

You can test the integration by creating, updating, or deleting Orc records and checking the logs and code field values.
