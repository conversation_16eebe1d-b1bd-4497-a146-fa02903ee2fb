<?php

namespace App\Services;

use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\OrshinSuugch;
use App\Models\OrshinSuugchToot;

class OrshinSuugchService
{
    public function __construct()
    {
    }

    public function getOrshinSuugchByPhone($phone): OrshinSuugch
    {
        return OrshinSuugch::where(OrshinSuugch::PHONE, $phone)->first();
    }

    public function importOs($orshinSuugchDatum)
    {
        $lastName      = $orshinSuugchDatum['last_name'];
        $name          = $orshinSuugchDatum['name'];
        $phone         = $orshinSuugchDatum['phone'];
        $email         = $orshinSuugchDatum['email'];
        $bairName      = $orshinSuugchDatum['bair_name'];
        $korpusName    = $orshinSuugchDatum['korpus_name'];
        $number        = $orshinSuugchDatum['number'];
        $stateBankCode = $orshinSuugchDatum['state_bank_code'];

        if (!isset($number))
            return;

        $orshinSuugch = OrshinSuugch::with(OrshinSuugch::RELATION_ORSHIN_SUUGCH_TOOTS)
                                    ->where(OrshinSuugch::PHONE, $phone)
                                    ->first();

        $service = resolve(UserInfoService::class);
        $sukh    = $service->getAUSukh();

        $bair   = Bair::where(Bair::NAME, $bairName)->first();
        $bairId = $bair->id;

        if (!isset($bairId))
            return;

        $korpus   = Korpus::where(Korpus::BAIR_ID, $bairId)->where(Korpus::NAME, $korpusName)->first();
        $korpusId = $korpus->id;

        if (!isset($korpusId))
            return;

        if (isset($orshinSuugch)) {
            if (!isset($orshinSuugch->uniq_code)) {
                $orshinSuugch->uniq_code = resolve(ToolService::class)->generateCodeFromDatetime();
                resolve(BPayService::class)->createBpayUser($orshinSuugch);
            }
            $orshinSuugch->last_name = $lastName;
            $orshinSuugch->name      = $name;
            $orshinSuugch->email     = $email;
            $orshinSuugch->save();
            if (count($orshinSuugch->sukhs) == 0)
                $orshinSuugch->sukhs()->save($sukh);

            if (count($orshinSuugch->orshin_suugch_toots) > 1)
                return;
            if (count($orshinSuugch->orshin_suugch_toots) == 1) {
                $orshinSuugchToot = $orshinSuugch->orshin_suugch_toots[0];
                if ($orshinSuugchToot->korpus_id == $korpusId && $orshinSuugchToot->number == $number)
                    return;
                $orshinSuugch->orshin_suugch_toots()->delete();
            }
            $orshinSuugch->orshin_suugch_toots()->create([
                OrshinSuugchToot::KORPUS_ID       => $korpusId,
                OrshinSuugchToot::NUMBER          => $number,
                OrshinSuugchToot::STATE_BANK_CODE => $stateBankCode
            ]);
        } else {
            $uniqCode        = resolve(ToolService::class)->generateCodeFromDatetime();
            $newOrshinSuugch = OrshinSuugch::create([
                OrshinSuugch::LAST_NAME => $lastName,
                OrshinSuugch::NAME      => $name,
                OrshinSuugch::PHONE     => $phone,
                OrshinSuugch::EMAIL     => $email,
                OrshinSuugch::IS_ADMIN  => true,
                OrshinSuugch::UNIQ_CODE => $uniqCode
            ]);
            $newOrshinSuugch->sukhs()->save($sukh);
            $newOrshinSuugch->orshin_suugch_toots()->create([
                OrshinSuugchToot::KORPUS_ID       => $korpusId,
                OrshinSuugchToot::NUMBER          => $number,
                OrshinSuugchToot::STATE_BANK_CODE => $stateBankCode
            ]);
            resolve(BPayService::class)->createBpayUser($newOrshinSuugch);
        }
    }
}
