<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class OrcTag extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'         => $this->id,
            'orc_id'     => $this->orc_id,
            'tag_code'   => $this->tag_code,
            'orc'        => new Orc($this->orc)
        ];
    }
}
