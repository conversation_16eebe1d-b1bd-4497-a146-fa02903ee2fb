<?php

namespace App\Http\Requests;

use App\Models\PackageMonth;
use Illuminate\Foundation\Http\FormRequest;

class GetPackageMonthsRequest extends FormRequest
{
    const PARAMETER_PACKAGE_ID              = 'package_id';
    const LIMIT                             = 'limit';
    const SORT                              = 'sort';
    const SORT_FIELD                        = 'field';
    const SORT_TYPE                         = 'type';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_PACKAGE_ID => 'required|numeric',
        ];
    }
}
