<?php

namespace App\Services;

use App\Models\Bair;
use App\Models\Korpus;
use App\Services\KorpusSyncService;
use Illuminate\Support\Facades\Log;

class BairSyncService
{
    protected KorpusSyncService $korpusSyncService;

    public function __construct(KorpusSyncService $korpusSyncService)
    {
        $this->korpusSyncService = $korpusSyncService;
    }

    /**
     * Sync Bair update with CVSecurity service
     * This will update all related Korpus buildings when Bair name changes
     *
     * @param Bair $bair
     * @return void
     */
    public function syncUpdate(Bair $bair): void
    {
        try {
            // Check if the name field was changed
            if (!$bair->wasChanged(Bair::NAME)) {
                Log::debug('BairSyncService: Bair updated but name was not changed, skipping sync', [
                    'bair_id' => $bair->id,
                    'changed_fields' => array_keys($bair->getChanges())
                ]);
                return;
            }

            Log::info('BairSyncService: Bair name changed, updating related Korpus buildings', [
                'bair_id' => $bair->id,
                'old_name' => $bair->getOriginal(Bair::NAME),
                'new_name' => $bair->name
            ]);

            // Get all Korpus records that belong to this Bair and have CVSecurity codes
            $korpuses = $bair->korpuses()->whereNotNull(Korpus::CODE)->get();

            if ($korpuses->isEmpty()) {
                Log::info('BairSyncService: No Korpus with CVSecurity codes found for this Bair', [
                    'bair_id' => $bair->id
                ]);
                return;
            }

            $successCount = 0;
            $failureCount = 0;

            // Update each Korpus building in CVSecurity with the new Bair name
            foreach ($korpuses as $korpus) {
                try {
                    // Ensure the Bair relationship is loaded with fresh data
                    $korpus->setRelation('bair', $bair);
                    
                    // Use the existing KorpusSyncService to update the building
                    $this->korpusSyncService->syncUpdate($korpus);
                    
                    $successCount++;
                    
                    Log::info('BairSyncService: Successfully updated Korpus building in CVSecurity', [
                        'bair_id' => $bair->id,
                        'korpus_id' => $korpus->id,
                        'korpus_name' => $korpus->name,
                        'cv_code' => $korpus->code
                    ]);

                } catch (\Exception $e) {
                    $failureCount++;
                    
                    Log::error('BairSyncService: Failed to update Korpus building in CVSecurity', [
                        'bair_id' => $bair->id,
                        'korpus_id' => $korpus->id,
                        'korpus_name' => $korpus->name,
                        'cv_code' => $korpus->code,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            Log::info('BairSyncService: Completed updating Korpus buildings for Bair name change', [
                'bair_id' => $bair->id,
                'total_korpuses' => $korpuses->count(),
                'successful_updates' => $successCount,
                'failed_updates' => $failureCount
            ]);

        } catch (\Exception $e) {
            Log::error('BairSyncService: Exception during Bair CVSecurity sync on update', [
                'bair_id' => $bair->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Sync all Korpus buildings for a Bair
     * This can be used for manual synchronization or recovery
     *
     * @param Bair $bair
     * @return array
     */
    public function syncAllKorpusBuildings(Bair $bair): array
    {
        $results = [
            'total' => 0,
            'successful' => 0,
            'failed' => 0,
            'skipped' => 0,
            'details' => []
        ];

        try {
            // Get all Korpus records that belong to this Bair
            $korpuses = $bair->korpuses()->get();
            $results['total'] = $korpuses->count();

            if ($korpuses->isEmpty()) {
                Log::info('BairSyncService: No Korpus found for this Bair', [
                    'bair_id' => $bair->id
                ]);
                return $results;
            }

            foreach ($korpuses as $korpus) {
                try {
                    if (!$korpus->code) {
                        // Korpus doesn't have a CVSecurity code, try to create it
                        $this->korpusSyncService->syncCreate($korpus);
                        $results['successful']++;
                        $results['details'][] = [
                            'korpus_id' => $korpus->id,
                            'action' => 'created',
                            'status' => 'success'
                        ];
                    } else {
                        // Korpus has a CVSecurity code, update it
                        $korpus->setRelation('bair', $bair);
                        $this->korpusSyncService->syncUpdate($korpus);
                        $results['successful']++;
                        $results['details'][] = [
                            'korpus_id' => $korpus->id,
                            'action' => 'updated',
                            'status' => 'success'
                        ];
                    }

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][] = [
                        'korpus_id' => $korpus->id,
                        'action' => $korpus->code ? 'update' : 'create',
                        'status' => 'failed',
                        'error' => $e->getMessage()
                    ];

                    Log::error('BairSyncService: Failed to sync Korpus building', [
                        'bair_id' => $bair->id,
                        'korpus_id' => $korpus->id,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            Log::info('BairSyncService: Completed syncing all Korpus buildings for Bair', [
                'bair_id' => $bair->id,
                'results' => $results
            ]);

        } catch (\Exception $e) {
            Log::error('BairSyncService: Exception during syncAllKorpusBuildings', [
                'bair_id' => $bair->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $results;
    }
}
