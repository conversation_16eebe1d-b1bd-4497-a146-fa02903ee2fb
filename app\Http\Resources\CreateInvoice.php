<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CreateInvoice extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id'                    => (int) $this->id,
            'invoice_code'          => $this->invoice_code,
            'sender_invoice_no'     => $this->sender_invoice_no,
            'invoice_receiver_code' => $this->invoice_receiver_code,
            'invoice_description'   => $this->invoice_description,
            'amount'                => (float) $this->amount,
            'status'                => $this->status,
            'package_id'            => $this->package_id,
            'package_name'          => $this->package_name,
            'package_products'      => $this->package_products,

            'invoice_id'            => $this->invoice_id,
            'qr_text'               => $this->qr_text,
            'qr_image'              => $this->qr_image,
            'qPay_shortUrl'         => $this->qPay_shortUrl,
            'urls'                  => $this->urls,
        ];
    }
}
