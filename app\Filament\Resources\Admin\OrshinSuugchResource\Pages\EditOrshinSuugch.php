<?php

namespace App\Filament\Resources\Admin\OrshinSuugchResource\Pages;

use App\Services\BPayService;
use App\Services\ToolService;

use App\Filament\Resources\Admin\OrshinSuugchResource;
use App\Models\OrshinSuugch;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOrshinSuugch extends EditRecord
{
    protected static string $resource = OrshinSuugchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function afterSave(): void
    {
        $orshinSuugch = $this->record;
        if (isset($orshinSuugch->uniq_code))
            return;
        $orshinSuugch->uniq_code = resolve(ToolService::class)->generateCodeFromDatetime();
        $response = resolve(BPayService::class)->createBpayUser($orshinSuugch);
        if (isset($response)) {
            $orshinSuugch->save();
            $this->data[OrshinSuugch::UNIQ_CODE] = $orshinSuugch->uniq_code;
        }
    }
}
