<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;
use Illuminate\Http\Request;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     */
    protected function redirectTo(Request $request): ?string
    {
        if ($request->expectsJson()) {
            return null;
        }

        // Check if the request is for a Filament panel
        if ($request->is('admin') || $request->is('admin/*')) {
            return route('filament.admin.auth.login');
        }

        if ($request->is('superadmin') || $request->is('superadmin/*')) {
            return route('filament.superadmin.auth.login');
        }

        // Default to admin panel login for other requests
        return route('filament.admin.auth.login');
    }
}
