# Davhar CVSecurity Integration Documentation

## Overview

This document describes the integration between the Davhar (Floor) model and the CVSecurity service. The integration automatically synchronizes CRUD operations on Davhars with the external CVSecurity system using the EleLevel (Elevator Level) entity.

## Features

- ✅ **Automatic Synchronization**: All CRUD operations on Davhars are automatically synced with CVSecurity
- ✅ **Error Handling**: Graceful handling of CVSecurity service unavailability
- ✅ **Logging**: Comprehensive logging of all sync operations
- ✅ **Code Storage**: CVSecurity response codes are stored in the `code` field
- ✅ **Hierarchical Structure**: Davhars are properly linked to their parent Orc (Entrance)
- ✅ **Door Management**: Support for door number ranges within each floor

## Architecture

### Components

1. **DavharObserver** (`app/Observers/DavharObserver.php`)
   - Listens to Davhar model events (created, updated, deleted)
   - Triggers appropriate CVSecurity sync operations

2. **DavharSyncService** (`app/Services/DavharSyncService.php`)
   - Handles the actual synchronization with CVSecurity
   - Manages error handling and logging
   - Extracts codes from CVSecurity responses
   - Maps Davhar data to CVSecurity EleLevel format

3. **CvSecurityServiceExt** (`app/Services/CvSecurityService/CvSecurityServiceExt.php`)
   - Extended CVSecurity service for API communication
   - Provides CRUD operations for EleLevel entities in CVSecurity
   - Includes createEleLevel, updateEleLevel, deleteEleLevel, and getEleLevelByCode methods

4. **Database Migration** (`database/migrations/2025_01_28_000001_create_davhars_table.php`)
   - Creates the `davhars` table with proper relationships and constraints

5. **Toot Relationship Migration** (`database/migrations/2025_01_28_000002_add_davhar_id_to_toots_table.php`)
   - Adds `davhar_id` foreign key to the `toots` table

## Hierarchical Structure

The Davhar entity fits into the existing hierarchical structure as follows:

```
Sukh (Residents' Committee)
└── Bair (Building)
    └── Korpus (Block)
        └── Orc (Entrance)
            └── Davhar (Floor) [NEW]
                └── Toot (Door)
```

### CVSecurity Entity Mapping

- **Davhar** → **EleLevel** (Elevator Level) in CVSecurity
- Parent relationship: Davhar belongs to Orc (which maps to Entrance in CVSecurity)
- Child relationship: Toot (Door) can belong to Davhar

## Database Schema

### Davhars Table

```sql
CREATE TABLE davhars (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    orc_id BIGINT NOT NULL,
    number VARCHAR(255) NOT NULL,
    order INT DEFAULT 1,
    begin_toot_number INT DEFAULT 0,
    end_toot_number INT DEFAULT 0,
    code VARCHAR(30) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (orc_id) REFERENCES orcs(id) ON DELETE CASCADE,
    UNIQUE KEY davhars_orc_id_order_unique (orc_id, order)
);
```

### Service Registration

The services are registered in `app/Providers/AppServiceProvider.php`:

```php
// Register DavharSyncService
$this->app->singleton(\App\Services\DavharSyncService::class);
```

### Observer Registration

The observer is registered in `app/Providers/AppServiceProvider.php`:

```php
// Register model observers
Davhar::observe(DavharObserver::class);
```

## Usage

### Automatic Synchronization

The integration works automatically. When you perform any CRUD operation on a Davhar:

```php
// Create - automatically syncs with CVSecurity
$davhar = Davhar::create([
    'orc_id' => 1,
    'number' => '4',
    'order' => 1,
    'begin_toot_number' => 1,
    'end_toot_number' => 6,
]);

// Update - automatically syncs with CVSecurity
$davhar->update([
    'number' => '5',
]);

// Delete - automatically syncs with CVSecurity
$davhar->delete();
```

### CVSecurity Data Format

When syncing with CVSecurity, the Davhar data is formatted as:

```php
[
    'name' => 'Floor 4',
    'remark' => 'Floor 4 in Entrance 1',
    'parent_code' => 'ENTR_000001', // Code of the parent Orc
]
```

## Relationships

### Model Relationships

```php
// Davhar belongs to Orc
$davhar->orc;

// Davhar has many Toots
$davhar->toots;

// Orc has many Davhars
$orc->davhars;

// Toot belongs to Davhar (optional)
$toot->davhar;
```

## Error Handling

The integration includes comprehensive error handling:

1. **Service Unavailability**: If CVSecurity service is not available, operations continue locally with null codes
2. **API Failures**: Failed API calls are logged with detailed error information
3. **Exception Handling**: All exceptions are caught and logged without breaking the application flow
4. **Infinite Loop Prevention**: Code-only updates are ignored to prevent observer loops

## Logging

All sync operations are logged with appropriate levels:

- **Info**: Successful operations
- **Warning**: Service unavailability
- **Error**: API failures and exceptions
- **Debug**: Detailed operation information

## Testing

To test the integration:

1. Create a new Davhar and verify it appears in CVSecurity
2. Update a Davhar and verify changes are reflected in CVSecurity
3. Delete a Davhar and verify it's removed from CVSecurity
4. Test error scenarios (service unavailable, invalid data)

## Door Numbering Integration

Davhars support door number ranges through the `begin_toot_number` and `end_toot_number` fields, which integrate with the door numbering system documented in the Door Numbering System documentation.
