<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateMemberRequest extends FormRequest
{
    const PARAMETER_LAST_NAME  = 'last_name';
    const PARAMETER_FIRST_NAME = 'first_name';

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            self::PARAMETER_LAST_NAME  => 'nullable|string',
            self::PARAMETER_FIRST_NAME => 'nullable|string',
        ];
    }
}
