<?php

namespace App\Services;

use App\Models\Bair;
use App\Models\Korpus;
use App\Models\NehemjlehTohirgooDtl;
use App\Models\Orc;
use App\Models\OrshinSuugchToot;
use App\Models\Toot;
use Illuminate\Support\Facades\Auth;

class UserInfoService
{
    public function __construct()
    {
    }

    public function getAUSukh()
    {
        $user = Auth::user();
        return $user->sukhs->first();
    }

    public function getAUBairs()
    {
        $sukh = $this->getAUSukh();
        return $sukh->bairs->pluck(Bair::NAME, Bair::ID);
    }

    public function getAUKorpuses($bairId)
    {
        $sukh = $this->getAUSukh();
        $bair = $sukh->bairs->find($bairId);
        return $bair ? $bair->korpuses()->pluck(Korpus::NAME, Korpus::ID) : null;
    }

    public function getAUOrcs($bairId)
    {
        $sukh = $this->getAUSukh();
        $bair = $sukh->bairs->find($bairId);
        return $bair ? $bair->orcs()->pluck(Orc::NUMBER, Orc::ID) : null;
    }

    public function getAUTootsKeyNumber($bairId, $korpusId)
    {
        if (!isset($bairId) || !isset($korpusId))
            return [];
        $sukh = $this->getAUSukh();
        $bair = $sukh->bairs->find($bairId);
        if (!isset($bair))
            return [];
        $korpus  = $bair->korpuses()->find($korpusId);
        if (!isset($korpus))
            return [];
        return $korpus->toots()->pluck(Toot::NUMBER, Toot::NUMBER);
    }

    public function getAUTootsKeyNumberWithDoesntHave($bairId, $korpusId)
    {
        if (!isset($bairId) || !isset($korpusId))
            return [];
        $sukh = $this->getAUSukh();
        $bair = $sukh->bairs->find($bairId);
        if (!isset($bair))
            return [];
        $korpus  = $bair->korpuses()->find($korpusId);
        if (!isset($korpus))
            return [];
        $nehemjlehTohirgooDtls = NehemjlehTohirgooDtl::where(NehemjlehTohirgooDtl::BAIR_ID, $bairId)->where(NehemjlehTohirgooDtl::KORPUS_ID, $korpusId)->get();

        $result   = [];
        $toots    = $korpus->toots()->get();
        foreach ($toots as $toot) {
            $hasFound = false;
            foreach ($nehemjlehTohirgooDtls as $nehemjlehTohirgooDtl) {
                if ($nehemjlehTohirgooDtl->bair_id != $bairId || $nehemjlehTohirgooDtl->korpus_id != $korpusId || $nehemjlehTohirgooDtl->number != $toot->number)
                    continue;
                $hasFound = true;
            }
            if (!$hasFound)
                $result[$toot->number] = $toot->number;
        }
        return $result;
    }
}
