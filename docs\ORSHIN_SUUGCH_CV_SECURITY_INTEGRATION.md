# OrshinSuugch CVSecurity Integration Documentation

## Overview

This document describes the integration between the OrshinSuugch model and the CVSecurity service. The integration automatically synchronizes CRUD operations on OrshinSuugch (person entities) with the external CVSecurity system using the main CVSecurity service (not CVSecurityServiceExt).

## Features

- ✅ **Automatic Synchronization**: All CRUD operations on OrshinSuugch are automatically synced with CVSecurity
- ✅ **Error Handling**: Graceful handling of CVSecurity service unavailability
- ✅ **Logging**: Comprehensive logging of all sync operations
- ✅ **Code Storage**: CVSecurity response codes (pins) are stored in the `code` field
- ✅ **Mock Implementation**: Mock implementation for getNextPersonPin endpoint (to be replaced when endpoint is available)
- ✅ **Testing**: Comprehensive test suite included

## Architecture

### Components

1. **OrshinSuugchObserver** (`app/Observers/OrshinSuugchObserver.php`)
   - Listens to OrshinSuugch model events (created, updated, deleted)
   - Triggers appropriate CVSecurity sync operations

2. **OrshinSuugchSyncService** (`app/Services/OrshinSuugchSyncService.php`)
   - Handles the actual synchronization with CVSecurity
   - Manages error handling and logging
   - Extracts codes from CVSecurity responses
   - Maps OrshinSuugch data to CVSecurity person format

3. **CvSecurityService** (`app/Services/CvSecurityService/CvSecurityService.php`)
   - Main CVSecurity service for API communication
   - Provides CRUD operations for persons in CVSecurity
   - Includes mock implementation for getNextPersonPin

4. **Database Migration** (`database/migrations/2025_01_27_130000_add_code_to_orshin_suugches_table.php`)
   - Adds the `code` field to the orshin_suugches table

## Database Schema Changes

### New Field: `code`

- **Type**: `string`, nullable
- **Purpose**: Stores the CVSecurity response pin
- **Location**: Added after `uniq_code` field in orshin_suugches table

```sql
ALTER TABLE orshin_suugches ADD COLUMN code VARCHAR(255) NULL AFTER uniq_code;
```

## Configuration

### Environment Variables

```env
CV_SECURITY_HOST=*************
CV_SECURITY_PORT=8098
CV_SECURITY_API_KEY=1234567890abcdef
```

### Service Registration

The services are registered in `app/Providers/AppServiceProvider.php`:

```php
// Register CvSecurityService
$this->app->singleton(\App\Services\CvSecurityService\CvSecurityService::class);

// Register OrshinSuugchSyncService
$this->app->singleton(\App\Services\OrshinSuugchSyncService::class);
```

### Observer Registration

The observer is registered in `app/Providers/AppServiceProvider.php`:

```php
// Register model observers
OrshinSuugch::observe(OrshinSuugchObserver::class);
```

## Usage

### Automatic Synchronization

The integration works automatically. When you perform any CRUD operation on an OrshinSuugch:

```php
// Create - automatically syncs with CVSecurity
$orshinSuugch = OrshinSuugch::create([
    'name' => 'Test Person',
    'last_name' => 'Test Last Name',
    'phone' => '99887766',
    'email' => '<EMAIL>',
    'is_admin' => false,
]);

// Update - automatically syncs with CVSecurity
$orshinSuugch->update(['name' => 'Updated Name']);

// Delete - automatically syncs with CVSecurity
$orshinSuugch->delete();
```

### Manual Synchronization

You can also manually trigger synchronization:

```php
$syncService = app(OrshinSuugchSyncService::class);

// Manual create sync
$syncService->syncCreate($orshinSuugch);

// Manual update sync
$syncService->syncUpdate($orshinSuugch);

// Manual delete sync
$syncService->syncDelete($orshinSuugch);
```

## API Endpoints Used

The integration uses the following CVSecurity API endpoints:

1. **Create/Update Person**: `POST /api/person/addPersonnelBasicInfo`
2. **Delete Person**: `POST /api/person/delete/{pin}`
3. **Get Person**: `GET /api/person/get/{pin}`

## Data Mapping

OrshinSuugch fields are mapped to CVSecurity person fields as follows:

| OrshinSuugch Field | CVSecurity Field | Notes |
|-------------------|------------------|-------|
| `code` | `pin` | Unique identifier |
| `name` | `name` | Person's first name |
| `last_name` | `lastName` | Person's last name |
| `phone` | `mobilePhone` | Mobile phone number |
| `email` | `email` | Email address |

## Mock Implementation

### getNextPersonPin

Since the "get next person pin" endpoint doesn't exist in CVSecurity yet, a mock implementation is provided:

```php
public function getNextPersonPin(): ?string
{
    // Mock implementation - generate a unique pin based on timestamp
    $timestamp = now()->format('YmdHis');
    $randomSuffix = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
    $pin = $timestamp . $randomSuffix;
    
    return $pin;
}
```

**Note**: This mock implementation should be replaced with the actual API call when the endpoint becomes available in CVSecurityServiceExt.

## Error Handling

The integration includes comprehensive error handling:

- **Service Unavailable**: Operations continue normally, but sync is skipped
- **API Errors**: Logged with full context for debugging
- **Network Issues**: Gracefully handled with appropriate logging
- **Invalid Responses**: Logged and handled without breaking the application

## Logging

All sync operations are logged with appropriate levels:

- **Info**: Successful operations and important state changes
- **Warning**: Service unavailable or non-critical issues
- **Error**: Failed operations and exceptions
- **Debug**: Detailed information for troubleshooting

## Testing

### Running Tests

```bash
# Run all OrshinSuugch CVSecurity integration tests
php artisan test tests/Feature/OrshinSuugchCvSecurityIntegrationTest.php

# Run specific test
php artisan test --filter=orshin_suugch_creation_triggers_cv_security_sync
```

### Test Coverage

The test suite covers:

- ✅ Model field validation
- ✅ Automatic sync on create/update/delete
- ✅ Service unavailability handling
- ✅ Code-only update skipping
- ✅ CVSecurity service methods
- ✅ Mock pin generation

## Troubleshooting

### Common Issues

1. **Code field not being set**
   - Check CVSecurity service availability
   - Verify API key configuration
   - Check logs for sync errors

2. **Infinite update loops**
   - The observer automatically skips code-only updates
   - Ensure you're not manually triggering sync in update operations

3. **Service unavailable**
   - Check network connectivity
   - Verify CVSecurity service is running
   - Check configuration values

### Debug Commands

```bash
# Check CVSecurity service status
php artisan tinker
>>> app(\App\Services\CvSecurityService\CvSecurityService::class)->isServiceAvailable()

# Check service configuration
>>> config('services.cv_security')

# Test manual sync
>>> $orshinSuugch = \App\Models\OrshinSuugch::first()
>>> app(\App\Services\OrshinSuugchSyncService::class)->syncCreate($orshinSuugch)
```

## Future Enhancements

1. **Replace Mock Implementation**: When the getNextPersonPin endpoint is available in CVSecurityServiceExt
2. **Batch Operations**: Support for bulk synchronization
3. **Retry Mechanism**: Automatic retry for failed sync operations
4. **Sync Status Tracking**: Track sync status and last sync time
5. **Admin Panel Integration**: Display sync status in Filament admin panel
